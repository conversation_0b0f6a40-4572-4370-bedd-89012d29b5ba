require_relative "boot"

require "rails/all"

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module Gastrostranky
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 8.0
    config.autoload_paths << "#{root}/app/views"
    config.autoload_paths << "#{root}/app/views/layouts"
    config.autoload_paths << "#{root}/app/views/components"
    config.autoload_paths << "#{root}/app/models/block_objects"
    config.autoload_paths << "#{root}/app/models/media_group_types"
    config.autoload_paths << "#{root}/app/models/media_field_types"
    config.autoload_paths << "#{root}/app/presenters"
    #  config.autoload_paths << "#{root}/app/presenters/controls"

    # Please, add to the `ignore` list any other `lib` subdirectories that do
    # not contain `.rb` files, or that should not be reloaded or eager loaded.
    # Common ones are `templates`, `generators`, or `middleware`, for example.
    config.autoload_lib(ignore: %w[assets tasks])

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    # config.time_zone = "Central Time (US & Canada)"
    # config.eager_load_paths << Rails.root.join("extras")
    I18n.available_locales = %i[cs sk en]
    I18n.default_locale = :cs

    config.time_zone = "Prague"

    ActiveStorage::Engine.config.active_storage.content_types_to_serve_as_binary.delete("image/svg+xml")

    config.autoload_paths += %W(#{config.root}/app/models/page_types)
  end
end
