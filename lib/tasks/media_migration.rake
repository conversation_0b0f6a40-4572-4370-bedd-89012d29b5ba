# lib/tasks/media_migration.rake
namespace :media_migration do
  desc "Migrate data from Media model to MediaContent model"
  task migrate_content: :environment do
    puts "Starting data migration for Media..."

    Media.find_each do |media|
      # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že všechna existující média jsou v primárním jazyce webu.
      # Upravte, pokud máte `locale` sloupec už teď.
      locale = media.website.locale || I18n.default_locale

      # Zkontrolujeme, jestli už pro toto médium a locale neexistuje obsah.
      next if media.contents.exists?(locale: locale)

      puts "Migrating Media ID: #{media.id}"

      # Převedeme stará data z `store_accessor` na `custom_fields`.
      # Upravte klíče podle toho, co jste měli v `store_accessor`.
      custom_data = {}
      
      # Přidáme data z jsonb sloupce, kromě těch, kter<PERSON> jsou nyní v pevn<PERSON>ch sloupcích
      if media.data.present?
        custom_data = media.data.except('caption') # caption je nyní pevný sloupec
        custom_data['job_position'] = media.data['job_position'] if media.data['job_position'].present?
        custom_data['media_url'] = media.data['media_url'] if media.data['media_url'].present?
        custom_data['url'] = media.data['url'] if media.data['url'].present?
      end

      begin
        media.contents.create!(
          locale: locale,
          # Pevné sloupce - čteme přímo z DB, abychom obešli delegaci
          title: media.read_attribute(:title),
          text: media.read_attribute(:text),
          content: media.read_attribute(:content),
          caption: media.read_attribute(:caption) || media.data&.dig('caption'),

          # Dynamická pole
          custom_fields: custom_data
        )
        puts "  ✓ Successfully migrated Media ID: #{media.id}"
      rescue => e
        puts "  ✗ Error migrating Media ID: #{media.id} - #{e.message}"
      end
    end

    puts "Data migration finished!"
  end

  desc "Verify migration data integrity"
  task verify_migration: :environment do
    puts "Verifying migration data integrity..."
    
    total_media = Media.count
    total_contents = MediaContent.count
    
    puts "Total Media records: #{total_media}"
    puts "Total MediaContent records: #{total_contents}"
    
    # Zkontrolujeme, že každé médium má alespoň jeden obsah
    media_without_content = Media.left_joins(:contents).where(media_contents: { id: nil }).count
    puts "Media without content: #{media_without_content}"
    
    if media_without_content == 0
      puts "✓ All media have content records"
    else
      puts "✗ Some media are missing content records"
    end
    
    # Zkontrolujeme duplicity
    duplicates = MediaContent.group(:media_id, :locale).having('COUNT(*) > 1').count
    if duplicates.empty?
      puts "✓ No duplicate content records found"
    else
      puts "✗ Found duplicate content records: #{duplicates.count}"
    end
  end
end
