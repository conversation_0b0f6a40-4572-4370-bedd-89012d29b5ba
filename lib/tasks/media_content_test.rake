# lib/tasks/media_content_test.rake
namespace :media_content do
  desc "Test MediaContent system functionality"
  task test: :environment do
    puts "🧪 Testing MediaContent system..."
    
    # Test 1: <PERSON><PERSON><PERSON><PERSON><PERSON>
    puts "\n1. Testing basic functionality..."
    media = Media.first
    if media
      puts "   ✓ Media loaded: #{media.id}"
      puts "   ✓ Title (delegated): '#{media.title}'"
      puts "   ✓ Contents count: #{media.contents.count}"
      puts "   ✓ Content for 'cs': #{media.content_for('cs').present? ? 'Present' : 'Missing'}"
    else
      puts "   ✗ No media found"
    end
    
    # Test 2: Vícejazyčnost
    puts "\n2. Testing multilingual support..."
    if media
      cs_content = media.content_for('cs')
      en_content = media.content_for('en')
      
      puts "   ✓ CS content: #{cs_content.persisted? ? 'Persisted' : 'New'}"
      puts "   ✓ EN content: #{en_content.persisted? ? 'Persisted' : 'New'}"
      
      # Test vytvoření EN obsahu
      if !en_content.persisted?
        en_content.title = "English Title"
        en_content.save
        puts "   ✓ Created EN content with title: '#{en_content.title}'"
      end
    end
    
    # Test 3: Custom fields
    puts "\n3. Testing custom fields..."
    if media&.media_type
      puts "   ✓ Media type: #{media.media_type.name}"
      content = media.content_for('cs')
      
      media.media_type.media_fields.each do |field|
        if !%w[title text content caption].include?(field.field_key)
          value = content.custom_field(field.field_key)
          puts "   ✓ Custom field '#{field.field_key}': '#{value}'"
        end
      end
    end
    
    # Test 4: Formulářová kompatibilita
    puts "\n4. Testing form compatibility..."
    if media
      # Test nested attributes
      params = {
        contents_attributes: {
          '0' => {
            id: media.content_for('cs').id,
            locale: 'cs',
            title: 'Updated Title',
            custom_fields: {
              'job_position' => 'Test Position'
            }
          }
        }
      }
      
      if media.update(params)
        puts "   ✓ Nested attributes update successful"
        puts "   ✓ Updated title: '#{media.title}'"
      else
        puts "   ✗ Nested attributes update failed: #{media.errors.full_messages}"
      end
    end
    
    puts "\n🎉 MediaContent system test completed!"
  end
end
