# Asynchronní překladatelský systém

Tento dokument popisuje implementaci a použití asynchronního překladatelského systému v Rails aplikaci.

## Přehled

Systém umožňuje automatický překlad obsahu pomocí DeepL API s následujícími vlastnostmi:

- **Asynchronní zpracování** - překlady se provádějí na pozadí pomocí Active Job
- **Polymorfní podpora** - podporuje překlad různých typů objektů (MediaContent, BlockControl)
- **Robustní error handling** - automatické opakování při chyb<PERSON>ch, logování
- **Idempotence** - zabraňuje duplicitním překladům
- **Sledování stavu** - kompletní audit trail všech překladatelských úloh

## Architektura

### Komponenty

1. **Translation<PERSON>ob (model)** - ActiveRecord model pro sledování úloh
2. **TranslationJob (Active Job)** - job pro asynchronní zpracování
3. **TranslationRequestService** - servisní objekt pro vytváření úloh
4. **Ai::DeepLTranslator** - klient pro komunikaci s DeepL API
5. **Admin::TranslationsController** - admin rozhraní pro správu překladů

### Workflow

1. Uživatel klikne na tlačítko "Přeložit do EN" v admin rozhraní
2. Controller zavolá `TranslationRequestService`
3. Service vytvoří záznam `TranslationJob` a zařadí úlohu do fronty
4. Solid Queue spustí `TranslationJob.perform_later`
5. Job zavolá `Ai::DeepLTranslator` pro překlad obsahu
6. Přeložený obsah se aplikuje na cílový objekt
7. Stav úlohy se aktualizuje na "completed" nebo "failed"

## Použití

### V admin rozhraní

Přidejte tlačítka pro překlad do formulářů:

```erb
<%= render 'admin/shared/translation_buttons', 
    translatable: @media_content, 
    size: 'md' %>
```

### Programatické použití

```ruby
# Vytvoření překladatelské úlohy
service = TranslationRequestService.new(media_content, 'en')
translation_job = service.call

# Kontrola stavu
translation_job.pending?     # => true
translation_job.processing?  # => false
translation_job.completed?   # => false
translation_job.failed?      # => false

# Ruční spuštění překladu (pro testování)
TranslationJob.perform_now(translation_job.id)
```

### Podporované objekty

#### MediaContent
Překládá se:
- `title` - titulek
- `text` - text
- `content` - obsah (ActionText)
- `caption` - popisek
- `custom_fields.*` - vlastní pole (pouze textové hodnoty)

#### BlockControl
Překládá se:
- `text` - textový obsah
- `options.*` - textové hodnoty v options hash
- `content.*` - textové hodnoty v content hash

## Konfigurace

### DeepL API

API klíč je nastaven v `app/services/ai/deep_l_translator.rb`:

```ruby
API_KEY = "1f0750d7-da68-4a86-a584-085381cf5d22:fx"
```

### Podporované jazyky

Mapování Rails locale na DeepL kódy:

```ruby
LOCALE_MAPPING = {
  'cs' => 'CS',
  'sk' => 'SK', 
  'en' => 'EN-US'
}
```

### Solid Queue

Konfigurace v `config/queue.yml`:

```yaml
default: &default
  dispatchers:
    - polling_interval: 1
      batch_size: 500
  workers:
    - queues: "*"
      threads: 3
      processes: 1
      polling_interval: 0.1
```

## Monitoring a správa

### Admin rozhraní

- **Seznam úloh**: `/admin/:website_id/translations`
- **Detail úlohy**: `/admin/:website_id/translations/:id`
- **Filtrování**: podle stavu a typu objektu
- **Akce**: opakování neúspěšných úloh, mazání dokončených úloh

### Stavy úloh

- **pending** - čeká na zpracování
- **processing** - právě se zpracovává
- **completed** - úspěšně dokončeno
- **failed** - selhalo (s chybovou zprávou)

### Retry strategie

```ruby
retry_on Ai::DeepLTranslator::APIError, wait: :exponentially_longer, attempts: 3
retry_on Faraday::TimeoutError, wait: 30.seconds, attempts: 2
retry_on Faraday::ConnectionFailed, wait: 10.seconds, attempts: 2

discard_on Ai::DeepLTranslator::UnsupportedLocaleError
discard_on ActiveRecord::RecordNotFound
```

## Rozšíření systému

### Přidání nového typu objektu

1. Přidejte typ do `SUPPORTED_TYPES` v `TranslationRequestService`
2. Implementujte metody `extract_*_content` a `apply_*_translation` v service a job
3. Ujistěte se, že objekt má `locale` atribut

### Přidání nového překladatelského poskytovatele

1. Vytvořte novou třídu implementující stejné rozhraní jako `Ai::DeepLTranslator`
2. Upravte `TranslationJob` pro použití nového poskytovatele
3. Aktualizujte mapování jazyků podle potřeby

## Testování

Spusťte testy:

```bash
# Testy služeb
rails test test/services/translation_request_service_test.rb
rails test test/services/ai/deep_l_translator_test.rb

# Testy modelů
rails test test/models/translation_job_test.rb

# Testy jobů
rails test test/jobs/translation_job_test.rb
```

## Troubleshooting

### Časté problémy

1. **Úlohy se nezpracovávají**
   - Zkontrolujte, že běží Solid Queue worker: `bin/jobs`
   - Ověřte konfiguraci v `config/queue.yml`

2. **DeepL API chyby**
   - Zkontrolujte API klíč a kvótu
   - Ověřte podporované jazykové páry

3. **Duplicitní úlohy**
   - Systém automaticky zabraňuje duplicitám pro aktivní úlohy
   - Po dokončení/selhání lze vytvořit novou úlohu

### Logování

Všechny důležité události jsou logovány:

```ruby
Rails.logger.info "Starting translation job #{@translation_job.id}"
Rails.logger.error "Translation job #{@translation_job.id} failed: #{e.message}"
```

## Bezpečnost

- API klíč je uložen přímo v kódu (pro produkci doporučujeme ENV proměnné)
- Všechny překladatelské úlohy jsou vázané na konkrétní website (multi-tenancy)
- Admin rozhraní vyžaduje autentifikaci

## Výkon

- Překlady se provádějí asynchronně, neblokují UI
- Solid Queue umožňuje škálování pomocí více workerů
- Databázové indexy optimalizují dotazy na úlohy
- Automatické čištění starých úloh (implementovat podle potřeby)
