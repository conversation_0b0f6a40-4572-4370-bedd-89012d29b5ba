class TranslationWorkerJob < ApplicationJob
  queue_as :default

  # TODO: P<PERSON>idat retry strategie po ověř<PERSON><PERSON> systému
  
  def perform(translation_job_id)
    @translation_job = TranslationJob.find(translation_job_id)
    
    Rails.logger.info "Starting translation job #{@translation_job.id}: #{@translation_job.description}"
    
    # Označíme úlohu jako <PERSON>
    @translation_job.mark_as_processing!
    
    # Provedeme překlad
    translated_content = translate_content
    
    # Aplikujeme přelo<PERSON>ený obsah na cílový objekt
    apply_translation(translated_content)
    
    # Označíme úlohu jako dokončenou
    @translation_job.mark_as_completed!(translated_content)
    
    Rails.logger.info "Translation job #{@translation_job.id} completed successfully"
    
  rescue => e
    Rails.logger.error "Translation job #{@translation_job.id} failed: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    
    # Označíme ú<PERSON> jako <PERSON>
    @translation_job.mark_as_failed!(e.message)
    
    # Re-raise pro Active Job retry mechanismus
    raise
  end
  
  private
  
  def translate_content
    translator_class = "Ai::DeepLTranslator".constantize
    translator = translator_class.new

    translator.call(
      content: @translation_job.source_content,
      source_locale: @translation_job.source_locale,
      target_locale: @translation_job.target_locale
    )
  end

  def translate_block_content
    block = @translation_job.translatable
    source_locale = @translation_job.source_locale
    target_locale = @translation_job.target_locale

    # Najdeme všechny controls pro zdrojový jazyk
    source_controls = block.controls_for_locale(source_locale)

    # Extrahujeme všechny texty k přeložení
    texts_to_translate = []
    source_controls.each do |control|
      texts_to_translate << control.text if control.text.present?

      # Přidáme texty z options podle typu
      case control.type
      when 'BlockControls::Button'
        if control.options&.dig('button_text').present?
          texts_to_translate << control.options['button_text']
        end
      when 'BlockControls::List'
        if control.content&.dig('items').is_a?(Array)
          control.content['items'].each do |item|
            texts_to_translate << item if item.is_a?(String) && item.present?
          end
        end
      end
    end

    # Odstraníme duplicity a prázdné texty
    texts_to_translate = texts_to_translate.compact.uniq.reject(&:blank?)

    return {} if texts_to_translate.empty?

    # Přeložíme texty
    translator_class = "Ai::DeepLTranslator".constantize
    translator = translator_class.new

    translated_texts = translator.translate_texts(
      texts_to_translate,
      source_locale,
      target_locale
    )

    # Vytvoříme mapování původní text -> přeložený text
    translation_map = {}
    texts_to_translate.each_with_index do |original_text, index|
      translation_map[original_text] = translated_texts[index] if translated_texts[index]
    end

    translation_map
  end
  
  def apply_translation(translated_content)
    if @translation_job.translatable.is_a?(MediaContent)
      apply_media_content_translation(translated_content)
    elsif @translation_job.translatable.is_a?(BlockControl)
      apply_block_control_translation_by_type(translated_content)
    else
      raise "Unsupported translatable type: #{@translation_job.translatable.class.name}"
    end
  end
  
  def apply_media_content_translation(translated_content)
    media = @translation_job.translatable.media
    target_locale = @translation_job.target_locale
    
    # Najdeme nebo vytvoříme MediaContent pro cílový jazyk
    target_content = media.contents.find_or_initialize_by(locale: target_locale)
    
    # Aplikujeme přeložené texty
    translated_content.each do |key, value|
      case key
      when 'title'
        target_content.title = value
      when 'text'
        target_content.text = value
      when 'content'
        target_content.content = value
      when 'caption'
        target_content.caption = value
      else
        # Custom fields (klíče ve formátu "custom_fields.field_name")
        if key.start_with?('custom_fields.')
          field_name = key.sub('custom_fields.', '')
          target_content.set_custom_field(field_name, value)
        end
      end
    end
    
    target_content.save!
    Rails.logger.info "Applied translation to MediaContent #{target_content.id} (locale: #{target_locale})"
  end
  
  def apply_block_control_translation_by_type(translated_content)
    source_control = @translation_job.translatable
    block = source_control.block
    target_locale = @translation_job.target_locale

    # Najdeme nebo vytvoříme BlockControl pro cílový jazyk
    target_control = block.controls.find_or_initialize_by(
      locale: target_locale,
      position: source_control.position,
      type: source_control.type
    )

    # Pokud je to nový control, zkopírujeme základní vlastnosti
    if target_control.new_record?
      target_control.assign_attributes(
        options: source_control.options,
        classes: source_control.classes,
        styles: source_control.styles,
        content: source_control.content,
        container: source_control.container
      )
    end

    # Aplikujeme překlad podle typu controlu
    case source_control.type
    when 'BlockControls::Heading'
      apply_heading_translation(source_control, target_control, translated_content)
    when 'BlockControls::Text'
      apply_text_translation(source_control, target_control, translated_content)
    when 'BlockControls::Button'
      apply_button_translation(source_control, target_control, translated_content)
    when 'BlockControls::List'
      apply_list_translation(source_control, target_control, translated_content)
    else
      # Pro neznámé typy použijeme obecný přístup
      apply_generic_translation(source_control, target_control, translated_content)
    end

    target_control.save!
    Rails.logger.info "Applied #{source_control.type} translation to BlockControl #{target_control.id} (locale: #{target_locale})"
  end
  
  def apply_heading_translation(source_control, target_control, translated_content)
    # Heading má obvykle jen text
    if translated_content['text']
      target_control.text = translated_content['text']
    end
  end

  def apply_text_translation(source_control, target_control, translated_content)
    # Text má obvykle jen text
    if translated_content['text']
      target_control.text = translated_content['text']
    end
  end

  def apply_button_translation(source_control, target_control, translated_content)
    # Button má text a možná button_text v options
    if translated_content['text']
      target_control.text = translated_content['text']
    end

    if translated_content['options.button_text']
      target_control.options = target_control.options.merge(
        'button_text' => translated_content['options.button_text']
      )
    end
  end

  def apply_list_translation(source_control, target_control, translated_content)
    # List má text a items v content
    if translated_content['text']
      target_control.text = translated_content['text']
    end

    # Aplikujeme přeložené položky seznamu
    translated_items = []
    index = 0
    while translated_content["content.items.#{index}"]
      translated_items << translated_content["content.items.#{index}"]
      index += 1
    end

    if translated_items.any?
      target_control.content = target_control.content.merge(
        'items' => translated_items
      )
    end
  end

  def apply_generic_translation(source_control, target_control, translated_content)
    # Pro neznámé typy použijeme obecný přístup
    translated_content.each do |key, value|
      apply_translated_field(target_control, key, value)
    end
  end

  def apply_translated_field(target_control, key, value)
    case key
    when 'text'
      target_control.text = value
    else
      # Složené klíče (options.field_name, content.field_name, atd.)
      parts = key.split('.')

      case parts.first
      when 'options'
        apply_nested_translation(target_control, :options, parts[1..-1], value)
      when 'content'
        apply_nested_translation(target_control, :content, parts[1..-1], value)
      end
    end
  end
  
  def apply_nested_translation(target_control, root_field, path, value)
    # Inicializujeme hash pokud neexistuje
    current_data = target_control.send(root_field) || {}
    current_data = current_data.dup # Vytvoříme kopii pro bezpečnost
    
    # Navigujeme k cílové pozici v nested struktuře
    current_level = current_data
    path[0..-2].each do |key|
      if key.match?(/\A\d+\z/) # Číselný index pro array
        index = key.to_i
        current_level[index] ||= {}
        current_level = current_level[index]
      else
        current_level[key] ||= {}
        current_level = current_level[key]
      end
    end
    
    # Nastavíme hodnotu
    final_key = path.last
    if final_key.match?(/\A\d+\z/) # Číselný index
      current_level[final_key.to_i] = value
    else
      current_level[final_key] = value
    end
    
    # Uložíme zpět do objektu
    target_control.send("#{root_field}=", current_data)
  end


end
