import { Controller } from "@hotwired/stimulus"
import React from 'react';
import { createRoot } from 'react-dom/client';
import TiptapEditor from '../../components/TiptapEditor.jsx';

export default class extends Controller {
    connect() {
        const container = this.element;

        // Zabráníme dvojité inicializaci, pokud by se connect zavolal vícekrát
        if (container.dataset.reactBooted) {
            return;
        }
        container.dataset.reactBooted = "true";

        // Přečteme všechna data z data-* atributů
        const {
            initialContent,
            htmlInputId,
            jsonInputId,
            previewElementId,
            editorClassName,
        } = container.dataset;

        // Zde je odstraněn typový cast 'as ...'
        const htmlInput = document.getElementById(htmlInputId);
        const jsonInput = document.getElementById(jsonInputId);
        const previewEl = document.getElementById(previewElementId);

        const parsedInitialContent = JSON.parse(initialContent || '""');
        const root = createRoot(container);

        root.render(
            <TiptapEditor
                initialContent={parsedInitialContent}
                editorClassName={editorClassName}
                onUpdateHTML={(html) => {
                    if (htmlInput) {
                        htmlInput.value = html;
                    }
                    if (previewEl) {
                        previewEl.innerHTML = html;
                    }
                }}
                onUpdateJSON={(json) => {
                    if (jsonInput) {
                        jsonInput.value = JSON.stringify(json);
                    }
                }}
            />
        );
    }
}