// app/javascript/components/TextColorPopover.jsx

import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';

// Ikona mů<PERSON>, ale jej<PERSON> dynamické podbarvení už nebude fungovat,
// protože nepracujeme s hex kódy. Uděláme ji statickou.
const TextColorIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M4.098 19.902a3.75 3.75 0 0 0 5.304 0l6.401-6.402M6.75 21A3.75 3.75 0 0 1 3 17.25V4.125C3 3.504 3.504 3 4.125 3h5.25c.621 0 1.125.504 1.125 1.125v4.072M6.75 21a3.75 3.75 0 0 0 3.75-3.75V8.197M6.75 21h13.125c.621 0 1.125-.504 1.125-1.125v-5.25c0-.621-.504-1.125-1.125-1.125h-4.072M10.5 8.197l2.88-2.88c.438-.439 1.15-.439 1.59 0l3.712 3.713c.44.44.44 1.152 0 1.59l-2.879 2.88M6.75 17.25h.008v.008H6.75v-.008Z"/>
    </svg>
);

const TextColorPopover = ({ editor, theme }) => {
    const [isOpen, setIsOpen] = useState(false);
    const popoverRef = useRef(null);

    // --- ZMĚNA ZDE: Paleta nyní obsahuje CSS třídy ---
    const textClassColors = [
        { name: 'Výchozí', class: null }, // Pro odstranění stylu
        { name: 'Accent Content', class: 'text-accent-content' },
        { name: 'Accent', class: 'text-accent' },
        { name: 'Instagram', class: 'text-instagram' },
    ];

    // useEffect pro zavření zůstává stejný...
    useEffect(() => { /* ... */ }, [popoverRef]);

    // --- ZMĚNA ZDE: Handler nyní volá `setClass` ---
    const handleSelectClass = (className) => {
        editor.chain().focus().setClass(className).run();
        setIsOpen(false);
    };

    // Zjistíme, jestli je nějaká třída aktivní, aby hlavní tlačítko mohlo reagovat
    const isActive = textClassColors.some(item => item.class && editor.isActive('classStyle', { class: item.class }));

    return (
        <div className="relative" ref={popoverRef}>
            <button
                type="button"
                onClick={() => setIsOpen(!isOpen)}
                title="Barva textu"
                className={`toggle-button flex w-7 h-7 justify-center items-center p-1 rounded-md ${isActive ? 'bg-gray-200' : 'hover:bg-gray-100'}`}
            >
                <TextColorIcon />
            </button>

            {isOpen && (
                <div data-theme={theme} className="absolute top-full mt-1 z-20 w-[200px] right-0 rounded-md border border-gray-200 bg-white p-2 shadow-lg">
                    <p className="text-xs text-gray-500 font-medium mb-1.5 px-0.5">Styly textu</p>
                    <div className="flex flex-wrap gap-2">
                        {textClassColors.map((item) => (
                            <button
                                key={item.name}
                                type="button"
                                onClick={() => handleSelectClass(item.class)}
                                title={item.name}
                                // --- ZMĚNA ZDE: Náhled stylu aplikujeme přímo na text ---
                                className={`px-2 py-1 text-sm rounded-md border ${editor.isActive('classStyle', { class: item.class }) ? 'border-blue-500' : 'border-gray-200'} hover:border-gray-400`}
                            >
                                <span className={item.class || ''}>Aa</span>
                            </button>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};

TextColorPopover.propTypes = {
    editor: PropTypes.object.isRequired,
};

export default TextColorPopover;