import React, { useState, useRef } from 'react';
import PropTypes from 'prop-types'; // Přidáme pro typovou kontrolu v JS
import { EditorContent, useEditor, BubbleMenu } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import TextStyle from '@tiptap/extension-text-style';
import Underline from '@tiptap/extension-underline';
import Highlight from '@tiptap/extension-highlight';
import { useDebouncedCallback } from 'use-debounce';
import HeadingDropdown from './HeadingDropdown.jsx';
import HighlightPopover from './HighlightPopover.jsx';
import TextColorPopover from './TextColorPopover.jsx';
import { ClassStyle } from './ClassStyle.jsx';

const EditorToolbar = ({ editor, theme }) => {
    // Typ `NodeJS.Timeout | null` je odstraněn
    const [isColorMenuVisible, setIsColorMenuVisible] = useState(false);
    const colorMenuTimer = useRef(null);

    if (!editor) {
        return null;
    }

    const showColorMenu = () => {
        if (colorMenuTimer.current) {
            clearTimeout(colorMenuTimer.current);
        }
        setIsColorMenuVisible(true);
    };

    const hideColorMenu = () => {
        colorMenuTimer.current = setTimeout(() => {
            setIsColorMenuVisible(false);
        }, 300);
    };

    // Typ `: string` u parametru `color` je odstraněn
    const toggleHighlight = (color) => {
        if (editor.isActive('highlight', { color })) {
            editor.chain().focus().unsetHighlight().run();
        } else {
            editor.chain().focus().toggleHighlight({ color }).run();
        }
    };

    const toggleColor = (color) => {
        if (editor.isActive('textColor', { color })) {
            editor.chain().focus().unsetColor().run();
        } else {
            editor.chain().focus().setColor(color).run();
        }
    };

    // Zbytek JSX zůstává stejný, je kompatibilní s JS
    return (
        <div className="bubble-menu text-black rounded-lg border border-gray-200 bg-white p-0 shadow-md">
            <div className="flex flex-wrap items-center space-x-1 sm:space-x-2 p-1">
                <div className="flex items-center border-r border-gray-200 mr-2 pr-2">
                    <HeadingDropdown editor={editor} />
                </div>

                <div className="flex items-center">
                    <button type="button" onClick={() => editor.chain().focus().toggleBold().run()} className={`toggle-button w-7 h-7 font-bold rounded-md ${editor.isActive('bold') ? 'bg-gray-200' : 'hover:bg-gray-100'}`} title="Tučné (Ctrl+B)">B</button>
                    <button type="button" onClick={() => editor.chain().focus().toggleItalic().run()} className={`toggle-button w-7 h-7 italic rounded-md ${editor.isActive('italic') ? 'bg-gray-200' : 'hover:bg-gray-100'}`} title="Kurzíva (Ctrl+I)">I</button>
                    <button type="button" onClick={() => editor.chain().focus().toggleUnderline().run()} className={`toggle-button w-7 h-7 underline rounded-md ${editor.isActive('underline') ? 'bg-gray-200' : 'hover:bg-gray-100'}`} title="Podtržené (Ctrl+U)">U</button>
                </div>

                <HighlightPopover editor={editor} />

                <TextColorPopover editor={editor} theme={theme} />
            </div>
        </div>
    );
};
// Kontrola typů pro EditorToolbar - dobrá praxe
EditorToolbar.propTypes = {
    editor: PropTypes.object
};

// --- Hlavní komponenta editoru ---
// `React.FC<TiptapEditorProps>` je odstraněno
const TiptapEditor = ({ initialContent = '', onUpdateHTML, onUpdateJSON, editorClassName = '', theme }) => {
    const debouncedUpdate = useDebouncedCallback(({ editor }) => {
        if (onUpdateHTML) onUpdateHTML(editor.getHTML());
        if (onUpdateJSON) onUpdateJSON(editor.getJSON());
    }, 1000);

    const editor = useEditor({
        extensions: [
            ClassStyle,
            StarterKit,
            TextStyle,
            Highlight.configure({ multicolor: true }),
            Underline,
        ],
        editorProps: {
            attributes: {
                class: editorClassName,
            },
        },
        content: initialContent,
        onUpdate: (props) => {
            debouncedUpdate(props);
        },
    });

    return (
        <div className="tiptap-editor-wrapper">
            {editor && (
                <BubbleMenu editor={editor} theme={theme} tippyOptions={{ duration: 100 }}>
                    <EditorToolbar editor={editor} theme={theme} />
                </BubbleMenu>
            )}
            <EditorContent data-theme={theme} editor={editor} />
        </div>
    );
};

// Dobrá praxe: Přidáme PropTypes jako náhradu za TypeScript rozhraní
TiptapEditor.propTypes = {
    initialContent: PropTypes.string,
    onUpdateHTML: PropTypes.func,
    onUpdateJSON: PropTypes.func,
    editorClassName: PropTypes.string,
    theme: PropTypes.string
};

export default TiptapEditor;