// app/javascript/components/ClassStyle.jsx

import { Mark } from '@tiptap/core';

export const ClassStyle = Mark.create({
    name: 'classStyle',

    addAttributes() {
        return {
            class: {
                default: null,
                renderHTML: attributes => {
                    if (!attributes.class) {
                        return {};
                    }
                    return {
                        class: attributes.class,
                    };
                },

                // --- ZMĚNA ZDE ---
                // Nyní vracíme přímo string, ne objekt.
                parseHTML: element => element.getAttribute('class'),
            },
        };
    },

    parseHTML() {
        return [
            {
                tag: 'span',
                getAttrs: node => (node.hasAttribute('class') ? {} : false),
            },
        ];
    },

    renderHTML({ HTMLAttributes }) {
        return ['span', HTMLAttributes, 0];
    },

    addCommands() {
        return {
            setClass: className => ({ commands }) => {
                if (!className) {
                    return commands.unsetMark(this.name);
                }
                return commands.setMark(this.name, { class: className });
            },
            unsetClass: () => ({ commands }) => {
                return commands.unsetMark(this.name);
            },
        };
    },
});