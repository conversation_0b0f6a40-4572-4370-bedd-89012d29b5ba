# == Schema Information
#
# Table name: blocks
#
#  id                  :bigint           not null, primary key
#  hidden_at           :datetime
#  media_options       :jsonb
#  name                :string
#  options             :jsonb
#  position            :integer
#  pricing_options     :jsonb
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  media_collection_id :bigint
#  media_type_id       :bigint
#  pricing_id          :bigint
#
# Indexes
#
#  index_blocks_on_media_collection_id  (media_collection_id)
#  index_blocks_on_media_type_id        (media_type_id)
#  index_blocks_on_pricing_id           (pricing_id)
#
# Foreign Keys
#
#  fk_rails_...  (media_collection_id => media_collections.id)
#  fk_rails_...  (media_collection_id => media_collections.id)
#  fk_rails_...  (media_type_id => media_types.id)
#  fk_rails_...  (pricing_id => pricing.id)
#
class Block < ApplicationRecord
  include ActionView::Helpers::SanitizeHelper
  include TailwindClassable

  LAYER_CONFIGS = {
    outer_container_layer: { class_name: "Block::OuterContainerLayer" },
    inner_container_layer: { class_name: "Block::InnerContainerLayer" },
    content_layer:         { class_name: "Block::ContentLayer" },
  }.freeze

  # Associations
  belongs_to :media_collection, optional: true
  belongs_to :pricing, optional: true
  belongs_to :media_type, optional: true

  has_many :page_blocks, dependent: :destroy
  has_many :layout_blocks, dependent: :destroy
  has_many :pages, through: :page_blocks
  has_many :controls, -> { order(position: :asc) }, class_name: "BlockControl", dependent: :destroy
  has_many :media, through: :media_collection, class_name: "Media", source: :media

  # Attachments
  has_one_attached :background_image do |attachable|
    attachable.variant :thumb, resize_to_fill: [120, 60]
    attachable.variant :preview, resize_to_limit: [ 1024, 768 ], saver: { quality: 100 }
  end

  has_one_attached :background_image_mobile

  # Scopes
  scope :visible, -> { where(hidden_at: nil) }

  # Store Accessor
  store_accessor :media_options, :inline_items_count, :posts_limit, :gap, :layout, :resize_image_options, :position, prefix: :media
  store_accessor :pricing_options, :pricing_type

  accepts_nested_attributes_for :controls, allow_destroy: true

  before_save :serialize_all_layers_to_options

  def controls_attributes=(attributes)
    super(attributes)
  end

  LAYER_CONFIGS.each do |layer_key, config|
    layer_class_str = config[:class_name]
    attributes_storage_key = "#{layer_key}_attributes"

    define_method(layer_key) do
      instance_variable_get("@#{layer_key}") || instance_variable_set("@#{layer_key}",
      begin
        layer_class = layer_class_str.safe_constantize
        unless layer_class
          Rails.logger.error "Chyba: Třída vrstvy '#{layer_class_str}' nenalezena pro klíč '#{layer_key}'."
          next nil
        end

        current_options_hash = self.options || {}
        layer_data_from_options = current_options_hash.fetch(attributes_storage_key, {})

        known_layer_attribute_names = layer_class.attribute_names.map(&:to_sym)
        attributes_for_poro = layer_data_from_options.deep_symbolize_keys.slice(*known_layer_attribute_names)

        final_attributes_for_poro = attributes_for_poro

        layer_class.new(final_attributes_for_poro)
      rescue => e
        Rails.logger.error "Chyba při inicializaci vrstvy #{layer_key} pro blok #{self.id || 'nový'}: #{e.message}\n#{e.backtrace.first(5).join("\n")}"
        layer_class.new(parent_block_id: self.id) if layer_class # Záložní instance jen s ID
      end
      )
    end

    def serialize_all_layers_to_options
      current_persisted_options = if persisted? && options_in_database.is_a?(Hash)
                                    options_in_database.deep_dup
                                  elsif self.options.is_a?(Hash)
                                    self.options.deep_dup
                                  else
                                    {}
                                  end

      final_options_to_save = current_persisted_options

      LAYER_CONFIGS.each_key do |layer_key|
        layer_poro_instance = instance_variable_get("@#{layer_key}")
        attributes_storage_key = "#{layer_key}_attributes"

        if layer_poro_instance
          final_options_to_save[attributes_storage_key] = layer_poro_instance.attributes.compact
        elsif !final_options_to_save.key?(attributes_storage_key)
          final_options_to_save[attributes_storage_key] = {}
        end
      end

      self.options = final_options_to_save
    end

    define_method("#{layer_key}_attributes=") do |new_attributes_hash|
      layer_object = send(layer_key)

      if layer_object.nil? && (layer_class = layer_class_str.safe_constantize)
        # Vytvoříme novou instanci s parent_block_id, pokud je to relevantní
        layer_object = layer_class.new(parent_block_id: self.id)
        instance_variable_set("@#{layer_key}", layer_object)
      end

      layer_object&.assign_attributes(new_attributes_hash)
      # Důležité: označí hlavní atribut `options` jako změněný,
      # protože jeho obsah (vnořený hash pro tuto vrstvu) se změní.
      self.options_will_change!
    end
  end

  # Callback pro serializaci všech vrstev do JSONB sloupce 'options' před uložením
  before_save :serialize_all_layers_to_options

  # Inicializace s výchozí strukturou pro nové záznamy
  after_initialize do
    if new_record?
      self.options ||= {} # Zajistí, že options je hash
      LAYER_CONFIGS.each_key do |layer_key|
        attributes_storage_key = "#{layer_key}_attributes"
        self.options[attributes_storage_key] ||= {} # Vytvoří prázdný hash pro každou vrstvu
        # Volitelně můžete zde rovnou zavolat send(layer_key) pro inicializaci PORO s defaulty
      end
    end
  end

  def serialize_all_layers_to_options
    self.options ||= {}
    LAYER_CONFIGS.each_key do |layer_key|
      layer_object = instance_variable_get("@#{layer_key}")

      if layer_object
        attributes_storage_key = "#{layer_key}_attributes"
        self.options[attributes_storage_key] = layer_object.attributes.compact
      elsif !self.options.key?("#{layer_key}_attributes") && !new_record?
      end
    end
  end

  def controls_for_locale(locale)
    controls.for_locale(locale)
  end

  def hidden?
    hidden_at.present?
  end

  def last?(page = nil)
    return false unless page
    page.blocks.last == self
  end

  def first?(page = nil)
    return false unless page
    page.blocks.first == self
  end

  def block_name
    heading_text = controls.find { |c| c.type == "BlockControls::Heading" }&.text
    heading_text.present? ? strip_tags(heading_text) : "block-#{id}"
  end

  def to_combobox_display
    block_name
  end

  def has_media?
    media_type.present?
  end
  def has_pricing?
    name == "pricing001"
  end

  def content_type

  end

  def theme
    content_layer.theme
  end

  def content_theme
    content_layer.theme_class || inner_container_layer.theme_class || outer_container_layer.theme_class
  end
end

