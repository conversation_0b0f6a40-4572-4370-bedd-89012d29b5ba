# == Schema Information
#
# Table name: pages
#
#  id                   :bigint           not null, primary key
#  ancestry             :string
#  ancestry_depth       :integer          default(0)
#  is_homepage          :boolean          default(FALSE)
#  link                 :string
#  locale               :string           default("cs")
#  meta_description     :string(500)
#  meta_title           :string
#  position             :integer
#  published_at         :datetime
#  slug                 :string
#  title                :string
#  type                 :string
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  anchor_block_id      :bigint
#  layout_id            :bigint
#  translation_group_id :bigint
#  website_id           :bigint
#
# Indexes
#
#  index_pages_on_ancestry              (ancestry)
#  index_pages_on_anchor_block_id       (anchor_block_id)
#  index_pages_on_layout_id             (layout_id)
#  index_pages_on_translation_group_id  (translation_group_id)
#  index_pages_on_website_id            (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (anchor_block_id => blocks.id)
#  fk_rails_...  (layout_id => layouts.id)
#  fk_rails_...  (translation_group_id => pages.id)
#  fk_rails_...  (website_id => websites.id)
#
class Page < ApplicationRecord
  extend FriendlyId
  friendly_id :slug_candidates, use: %i[scoped slugged], scope: %i[website_id]
  has_ancestry cache_depth: true

  validates :title, presence: true, length: { minimum: 3 }
  validates :link, presence: true, if: :is_link?

  belongs_to :layout, optional: true

  has_many :page_blocks, -> { order(position: :asc) }, dependent: :destroy
  has_many :blocks, through: :page_blocks
  has_many :html_tags, dependent: :destroy
  accepts_nested_attributes_for :html_tags, allow_destroy: true, reject_if: :all_blank

  belongs_to :anchor_block, class_name: "Block", optional: true

  # Translation system
  belongs_to :translation_master, class_name: "Page", foreign_key: :translation_group_id, optional: true
  has_many :translations, class_name: "Page", foreign_key: :translation_group_id, dependent: :nullify

  attr_writer :custom_slug

  scope :published, -> { where(published_at: ..Time.current) }

  acts_as_tenant(:website)
  positioned on: %i[website_id locale ancestry]

  # Callbacks
  after_create :set_translation_group_id, if: -> { translation_group_id == id }

  def slug_candidates
    [
      :custom_slug,
      :title,
      %i[id title]
    ]
  end

  def custom_slug
    @custom_slug || slug
  end

  def should_generate_new_friendly_id?
    true
  end

  def is_link?
    type == "Link"
  end

  def is_anchor?
    type == "Anchor"
  end

  def cta?
    id == 20
  end

  def anchor_page
    anchor_block.page
  end

  def anchor
    "block-container-#{anchor_block&.id}"
  end

  def to_combobox_display
    title
  end

  def self.homepage
    Page.where(is_homepage: true).take
  end

  def type_icon
    case type
    when "Content"
      '<svg class="-ml-1 h-4 w-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
      </svg>'.html_safe
    when "Link"
      '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-green-600">
        <path stroke-linecap="round" stroke-linejoin="round" d="M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244" />
      </svg>'.html_safe
    when "Anchor"
      '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-purple-600">
        <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 3.75V16.5L12 14.25 7.5 16.5V3.75m9 0H18A2.25 2.25 0 0 1 20.25 6v12A2.25 2.25 0 0 1 18 20.25H6A2.25 2.25 0 0 1 3.75 18V6A2.25 2.25 0 0 1 6 3.75h1.5m9 0h-9" />
      </svg>'.html_safe
    else
      '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-gray-400">
        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
      </svg>'.html_safe
    end
  end

  # Translation methods
  def all_translations
    Page.where(translation_group_id: translation_group_id)
  end

  def other_translations
    all_translations.where.not(id: id)
  end

  def translation_for(locale)
    all_translations.find_by(locale: locale.to_s)
  end

  def is_translation_master?
    translation_group_id == id
  end

  def available_translation_locales
    all_translations.pluck(:locale)
  end

  def missing_translation_locales
    current_tenant.available_locales - available_translation_locales
  end

  private

  def set_translation_group_id
    # Tato metoda se volá pouze pokud translation_group_id == id (nová hlavní stránka)
    # Nic neděláme, protože hodnota už je správně nastavená
  end
end
