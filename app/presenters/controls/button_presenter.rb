class Controls::ButtonPresenter < BasePresenter
  attr_reader :id,
              :primary_button_text, :primary_button_link, :primary_button_email,
              :primary_button_phone, :primary_button_link_type, :primary_button_page_id,
              :secondary_button_text, :secondary_button_link,
              :secondary_button_link_type, :secondary_button_page_id

  DEFAULT_VALUES = {
    primary_button_text: "Zobrazit ceník",
    primary_button_link: "#",
    primary_button_email: "",
    primary_button_phone: "",
    primary_button_link_type: "page",
    primary_button_page_id: "15", # Původní výchozí hodnota

    secondary_button_text: "Zjistit více",
    secondary_button_link: "https://www.seznam.cz", # Původní výchozí hodnota
    secondary_button_link_type: "link",
    secondary_button_page_id: "0" # Původní výchozí hodnota
  }.freeze

  def initialize(config, context)
    super(config, context) # Nastav<PERSON> @config a @context

    @id = @config[:id] # ID obvykle nemá výchozí hodnotu v tomto kontextu

    # Nastavení atributů s použitím výchozích hodnot, pokud nejsou v konfiguraci
    @primary_button_text      = get_value_with_fallback(:primary_button_text)
    @primary_button_link      = get_value_with_fallback(:primary_button_link)
    @primary_button_email     = get_value_with_fallback(:primary_button_email)
    @primary_button_phone     = get_value_with_fallback(:primary_button_phone)
    @primary_button_link_type = get_value_with_fallback(:primary_button_link_type)
    @primary_button_page_id   = get_value_with_fallback(:primary_button_page_id)

    @secondary_button_text      = get_value_with_fallback(:secondary_button_text)
    @secondary_button_link      = get_value_with_fallback(:secondary_button_link)
    @secondary_button_link_type = get_value_with_fallback(:secondary_button_link_type)
    @secondary_button_page_id   = get_value_with_fallback(:secondary_button_page_id)
  end

  def component
    BlockControls::ButtonControl.new(self)
  end

  def type
    "BlockControls::Button"
  end

  # Metoda options nyní vrací aktuální nakonfigurované (nebo výchozí) hodnoty.
  # Používá symboly jako klíče pro konzistenci.
  def options
    {
      primary_button_text: @primary_button_text,
      primary_button_link: @primary_button_link,
      primary_button_email: @primary_button_email,
      primary_button_phone: @primary_button_phone,
      primary_button_link_type: @primary_button_link_type,
      primary_button_page_id: @primary_button_page_id,

      secondary_button_text: @secondary_button_text,
      secondary_button_link: @secondary_button_link,
      secondary_button_link_type: @secondary_button_link_type,
      secondary_button_page_id: @secondary_button_page_id
    }.compact
  end

  private

  def get_value_with_fallback(key)
    control_specific_options = @config.fetch(:options, {})

    if control_specific_options.key?(key)
      control_specific_options[key]
    elsif @config.key?(key)
      @config[key]
    else
      DEFAULT_VALUES[key] # Vrátí nil, pokud klíč není ani ve výchozích hodnotách
    end
  end
end