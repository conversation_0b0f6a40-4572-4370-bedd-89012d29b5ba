<div class="flex space-x-2 items-center">
  <div class="flex items-center -space-x-3 flex-shrink-0">
    <% hw_combobox_search_image.media.limit(3).each do |media| %>
      <% if media.image.attached? %>
        <%= image_tag media.image.variant(:thumb), class: "rounded-full border border-gray-300 p-px w-8 h-8" %>
      <% end %>
    <% end %>

    <% if hw_combobox_search_image.media.count > 3 %>
      <div class="rounded-full border border-gray-300 p-px w-8 h-8 bg-gray-100 text-gray-600 flex items-center justify-center text-xs">
        +<%= hw_combobox_search_image.media.count - 3 %>
      </div>
    <% end %>
  </div>

  <span class="text-xs truncate overflow-hidden whitespace-nowrap text-gray-700">
    <%= hw_combobox_search_image.name %>
  </span>
</div>
