<div class="container mx-auto py-6">
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-2xl font-bold">Média</h1>
      <p class="text-gray-500">Blok: <%= @block.block_name %></p>
    </div>

    <% if @block.media_collection&.media_type %>
      <%= link_to new_admin_content_medium_path(block_id: @block.id, media_type_id: @block.media_collection.media_type.id), class: "btn btn-primary" do %>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
          <path d="M8.75 3.75a.75.75 0 0 0-1.5 0v3.5h-3.5a.75.75 0 0 0 0 1.5h3.5v3.5a.75.75 0 0 0 1.5 0v-3.5h3.5a.75.75 0 0 0 0-1.5h-3.5v-3.5Z" />
        </svg>
        <span>Přidat <%= @block.media_collection.media_type.name %></span>
      <% end %>
    <% elsif @block.media_type.present? %>
      <% media_type = MediaType.find_by(name: @block.media_type) %>
      <% if media_type %>
        <%= link_to new_admin_content_medium_path(block_id: @block.id, media_type_id: media_type.id), class: "btn btn-primary" do %>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
            <path d="M8.75 3.75a.75.75 0 0 0-1.5 0v3.5h-3.5a.75.75 0 0 0 0 1.5h3.5v3.5a.75.75 0 0 0 1.5 0v-3.5h3.5a.75.75 0 0 0 0-1.5h-3.5v-3.5Z" />
          </svg>
          <span>Přidat <%= media_type.name %></span>
        <% end %>
      <% end %>
    <% end %>
  </div>

  <div class="bg-white shadow overflow-hidden rounded-lg">
    <% if @block.media.any? %>
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Název</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Typ</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vytvořeno</th>
            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Akce</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% @block.media.each do |medium| %>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <% if medium.image.attached? %>
                    <%= image_tag medium.image.variant(:thumb), class: "w-8 h-8 rounded-full mr-3" %>
                  <% elsif medium.icon %>
                    <div class="w-8 h-8 flex items-center justify-center mr-3">
                      <%= medium.icon.with(size: 5) %>
                    </div>
                  <% end %>
                  <div class="text-sm font-medium text-gray-900"><%= medium.title %></div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500"><%= medium.media_type&.name || "Neznámý typ" %></div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500"><%= medium.created_at.strftime("%d.%m.%Y %H:%M") %></div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <%= link_to edit_admin_content_medium_path(medium, block_id: @block.id), class: "text-indigo-600 hover:text-indigo-900 mr-3" do %>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4 inline">
                    <path d="M13.488 2.513a1.75 1.75 0 0 0-2.475 0L6.75 6.774a2.75 2.75 0 0 0-.596.892l-.848 2.047a.75.75 0 0 0 .98.98l2.047-.848a2.75 2.75 0 0 0 .892-.596l4.261-4.262a1.75 1.75 0 0 0 0-2.474Z" />
                    <path d="M4.75 3.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h6.5c.69 0 1.25-.56 1.25-1.25V9A.75.75 0 0 1 14 9v2.25A2.75 2.75 0 0 1 11.25 14h-6.5A2.75 2.75 0 0 1 2 11.25v-6.5A2.75 2.75 0 0 1 4.75 2H7a.75.75 0 0 1 0 1.5H4.75Z" />
                  </svg>
                <% end %>

                <%= button_to admin_content_media_path(medium, block_id: @block.id), method: :delete, class: "text-red-600 hover:text-red-900 inline", form: { data: { turbo_confirm: "Opravdu chcete smazat tuto položku?" } } do %>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4 inline">
                    <path fill-rule="evenodd" d="M5 3.25V4H2.75a.75.75 0 0 0 0 1.5h.3l.815 8.15A1.5 1.5 0 0 0 5.357 15h5.285a1.5 1.5 0 0 0 1.493-1.35l.815-8.15h.3a.75.75 0 0 0 0-1.5H11v-.75A2.25 2.25 0 0 0 8.75 1h-1.5A2.25 2.25 0 0 0 5 3.25Zm2.25-.75a.75.75 0 0 0-.75.75V4h3v-.75a.75.75 0 0 0-.75-.75h-1.5ZM6.05 6a.75.75 0 0 1 .787.713l.275 5.5a.75.75 0 0 1-1.498.075l-.275-5.5A.75.75 0 0 1 6.05 6Zm3.9 0a.75.75 0 0 1 .712.787l-.275 5.5a.75.75 0 0 1-1.498-.075l.275-5.5a.75.75 0 0 1 .786-.711Z" clip-rule="evenodd" />
                  </svg>
                <% end %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    <% else %>
      <div class="p-6 text-center text-gray-500">
        Zatím nejsou přidána žádná média. Klikněte na tlačítko "Přidat" pro přidání nového média.
      </div>
    <% end %>
  </div>
</div>
