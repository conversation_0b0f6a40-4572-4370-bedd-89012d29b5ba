<%= turbo_frame_tag :media_sidebar do %>
  <style>
      .blocks-container { pointer-events: none;  }
      .block-overlay-lg { display: block !important; }
      .block-overlay { display: block !important; }
      .editable-header { display: none }
      #edit-block-container-<%= @block.id %> .block-overlay { display: none !important; }
      #edit-block-container-<%= @block.id %> { pointer-events: all !important; }
      #edit-block-container-<%= @block.id %> .block-overlay-lg { display: none !important; }
      .block-toolbar { display: none !important; }
      .block-editable-item { display: block !important; }
  </style>

  <script>
      const el = document.getElementById('block-container-<%= @block.id %>')

      if (el) {
          document.getElementById('block-container-<%= @block.id %>').scrollIntoView({
              behavior: 'auto',
              block: 'center',
              inline: 'center'
          });
      }
  </script>

  <div class="top-0 left-0 w-80 h-screen bg-gray-50 overflow-y-auto z-[200]">
    <div
      class="bg-left-0 top-0 h-screen fixed w-80 bg-gray-50 overflow-y-auto z-[200]"
    >
      <div class="w-full flex justify-between pt-4 px-3">
        <div>
          <a href="<%= edit_polymorphic_path([:admin, :content, @page, @block]) %>" data-turbo-method="get" data-turbo-confirm="Zavřít bez uložení?" class="p-1 rounded-md hover:bg-gray-200 text-gray-800 flex space-x-2">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
            </svg>
          </a>
        </div>
      </div>

      <div class="w-80 relative px-3">
        <div class="overflow-y-scroll" data-controller="tabs" data-tabs-active-tab-class="-mb-px border-b-2 border-gray-800 text-black">
          <%= render "media_form", medium: @medium, block: @block, page: @page %>
        </div>
      </div>
    </div>
  </div>
<% end %>