<ul class="mt-4 flex flex-col space-y-1.5" id="media-items" data-controller="sortable" data-sortable-handle-value=".handle" data-sortable-response-kind-value="turbo-stream">
  <% block.media.each do |medium| %>
    <li class="flex justify-between items-center w-full p-1 rounded border border-gray-200" id="<%= dom_id(medium) %>" data-sortable-update-url="<%= sort_admin_content_medium_path(medium, block_id: block.id) %>">
      <div class="flex items-center p-1 space-x-1.5">
        <button class="handle text-gray-300 hover:text-gray-900 cursor-move">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
            <path fill-rule="evenodd" d="M2 3.75A.75.75 0 0 1 2.75 3h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 3.75ZM2 8a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 8Zm0 4.25a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1-.75-.75Z" clip-rule="evenodd" />
          </svg>
        </button>

        <% if medium.image.attached? %>
          <%= image_tag medium.image.variant(:thumb), class: "w-6 h-6 rounded-full" %>
        <% elsif medium.icon %>
          <div class="w-6 h-6 flex items-center justify-center">
            <%= medium.icon.with(size: 4) %>
          </div>
        <% end %>

        <span class="text-sm"><%= medium.title %></span>
      </div>

      <div class="flex items-center space-x-2 mr-1.5">
        <%= button_to admin_content_medium_path(medium, block_id: block.id, page_id: page.id), method: :delete, class: "text-red-400 rounded-full cursor-pointer bg-red-50 p-1 pointer hover:bg-red-100 hover:text-red-500", form: { data: { turbo_confirm: "Opravdu chcete smazat tuto položku?" } } do %>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
            <path fill-rule="evenodd" d="M5 3.25V4H2.75a.75.75 0 0 0 0 1.5h.3l.815 8.15A1.5 1.5 0 0 0 5.357 15h5.285a1.5 1.5 0 0 0 1.493-1.35l.815-8.15h.3a.75.75 0 0 0 0-1.5H11v-.75A2.25 2.25 0 0 0 8.75 1h-1.5A2.25 2.25 0 0 0 5 3.25Zm2.25-.75a.75.75 0 0 0-.75.75V4h3v-.75a.75.75 0 0 0-.75-.75h-1.5ZM6.05 6a.75.75 0 0 1 .787.713l.275 5.5a.75.75 0 0 1-1.498.075l-.275-5.5A.75.75 0 0 1 6.05 6Zm3.9 0a.75.75 0 0 1 .712.787l-.275 5.5a.75.75 0 0 1-1.498-.075l.275-5.5a.75.75 0 0 1 .786-.711Z" clip-rule="evenodd" />
          </svg>
        <% end %>

        <%= link_to edit_admin_content_medium_path(medium, block_id: block.id, page_id: page.id), class: "text-gray-500 rounded-full bg-gray-100 p-1 cursor-pointer hover:bg-gray-200 hover:text-gray-900", data: { turbo_frame: "media_sidebar" } do %>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
            <path d="M13.488 2.513a1.75 1.75 0 0 0-2.475 0L6.75 6.774a2.75 2.75 0 0 0-.596.892l-.848 2.047a.75.75 0 0 0 .98.98l2.047-.848a2.75 2.75 0 0 0 .892-.596l4.261-4.262a1.75 1.75 0 0 0 0-2.474Z" />
            <path d="M4.75 3.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h6.5c.69 0 1.25-.56 1.25-1.25V9A.75.75 0 0 1 14 9v2.25A2.75 2.75 0 0 1 11.25 14h-6.5A2.75 2.75 0 0 1 2 11.25v-6.5A2.75 2.75 0 0 1 4.75 2H7a.75.75 0 0 1 0 1.5H4.75Z" />
          </svg>
        <% end %>
      </div>
    </li>
  <% end %>
</ul>
