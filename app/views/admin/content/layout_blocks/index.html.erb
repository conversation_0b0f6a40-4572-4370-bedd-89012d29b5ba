<div class="flex">
  <%= turbo_frame_tag :sidebar %>

  <div class="flex-1">
    <div class="flex flex-col" style="height: calc(100vh - 4rem);">
      <header class="flex-shrink-0">
        <div>
          <% if (block = @layout_models[:main_header]) %>
            <%= render "main_header_wrapper",
                       block: block,
                       component: @layout_components[:main_header],
                       owner: @owner, first: true, last: true %>
          <% else %>
            <div class="p-4 bg-white border-b border-gray-100 flex flex-col justify-center items-center">
              <p class="text-center text-gray-400 p-4 text-sm">Hlavní záhlaví není př<PERSON>.</p>
              <%= link_to new_admin_content_layout_block_path(@layout, add: 'header', location: 'main_header'), data: { turbo_frame: "sidebar" }, class: "btn btn-neutral btn-xs" do %>
                Přidat
              <% end %>
            </div>
          <% end %>
        </div>
        <div>
          <% if @layout_models[:global_headers].any? %>
            <% @layout_models[:global_headers].each_with_index do |block_item, index| %>
              <%= render "admin/content/page_blocks/block", block: block_item, component: @layout_components[:global_headers][index], owner: @owner, locale: @layout_locale, first: index.zero?, last: index == @layout.global_header_blocks.size - 1 %>
            <% end %>
          <% end %>
        </div>
      </header>

      <main class="flex-1 relative bg-gray-100 py-36 flex items-center justify-center">
              <span class="bg-gray-100 px-3 text-2xl font-medium text-gray-500 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-5 text-gray-400"><path fill-rule="evenodd" d="M2 2.5A2.5 2.5 0 0 1 4.5 0h7A2.5 2.5 0 0 1 14 2.5v11a2.5 2.5 0 0 1-2.5 2.5h-7A2.5 2.5 0 0 1 2 13.5v-11ZM4 4.75A.75.75 0 0 1 4.75 4h6.5a.75.75 0 0 1 0 1.5h-6.5A.75.75 0 0 1 4 4.75ZM4.75 12a.75.75 0 0 0 0-1.5h6.5a.75.75 0 0 0 0 1.5h-6.5Z" clip-rule="evenodd" /></svg>
                Obsah stránky
              </span>

      </main>

      <footer class="flex-shrink-0">
        <div>
          <% if @layout_models[:global_footers].any? %>
            <% @layout_models[:global_footers].each_with_index do |block_item, index| %>
              <%= render "admin/content/page_blocks/block", block: block_item, component: @layout_components[:global_footers][index], owner: @owner, locale: @layout_locale, first: index.zero?, last: index == @layout.global_header_blocks.size - 1 %>
            <% end %>
          <% end %>
        </div>
        <div>
          <% if (block = @layout_models[:main_footer]) %>
            <%= render "main_footer_wrapper",
                       block: block,
                       component: @layout_components[:main_footer],
                       owner: @owner, first: true, last: true %>
          <% else %>
            <div class="p-4 bg-white border-b border-gray-100 flex flex-col justify-center items-center">
              <p class="text-center text-gray-400 p-4 text-sm">Hlavní patička není přiřazena.</p>
              <%= link_to new_admin_content_layout_block_path(@layout, add: 'footer', location: 'main_footer'), data: { turbo_frame: "sidebar" }, class: "btn btn-neutral btn-xs" do %>
                Přidat
              <% end %>
            </div>
          <% end %>
        </div>
      </footer>

    </div>
  </div>
</div>
