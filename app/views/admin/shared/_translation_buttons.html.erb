<%
  # Parametry:
  # - translatable: objekt k <PERSON>ř<PERSON>í (MediaContent nebo BlockControl)
  # - available_locales: pole dostupn<PERSON><PERSON> (volitelné, default z current_tenant)
  # - current_locale: aktuální jazyk objektu (volitelné, pokusí se určit automaticky)
  # - size: velikost tlačítek ('sm', 'md', 'lg') - default 'sm'
  
  translatable = local_assigns[:translatable]
  available_locales = local_assigns[:available_locales] || current_tenant&.available_locales || I18n.available_locales
  current_locale = local_assigns[:current_locale] || (translatable.respond_to?(:locale) ? translatable.locale : I18n.default_locale)
  size = local_assigns[:size] || 'sm'
  
  # Určíme CSS třídy podle velikosti
  button_classes = case size
  when 'lg'
    "inline-flex items-center rounded-md px-3 py-2 text-sm font-semibold shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
  when 'md'
    "inline-flex items-center rounded-md px-2.5 py-1.5 text-sm font-semibold shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
  else # 'sm'
    "inline-flex items-center rounded px-2 py-1 text-xs font-medium shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
  end
  
  # Filtrujeme jazyky - zobrazíme pouze ty, které nejsou aktuální jazyk objektu
  target_locales = available_locales.reject { |locale| locale.to_s == current_locale.to_s }
%>

<% if translatable && target_locales.any? %>
  <div class="flex flex-wrap gap-2">
    <% if size != 'sm' %>
      <span class="text-sm font-medium text-gray-700">Přeložit do:</span>
    <% end %>
    
    <% target_locales.each do |target_locale| %>
      <%
        # Kontrola, zda již existuje aktivní překladatelská úloha
        existing_job = TranslationJob.where(
          translatable: translatable,
          target_locale: target_locale.to_s
        ).active.first
        
        # Určíme stav tlačítka
        if existing_job
          if existing_job.pending?
            button_state = 'pending'
            button_text = "#{target_locale.to_s.upcase} (čeká)"
            button_disabled = true
            button_color = "bg-yellow-50 text-yellow-700 ring-yellow-600/20"
          elsif existing_job.processing?
            button_state = 'processing'
            button_text = "#{target_locale.to_s.upcase} (zpracovává)"
            button_disabled = true
            button_color = "bg-blue-50 text-blue-700 ring-blue-600/20"
          end
        else
          button_state = 'ready'
          button_text = target_locale.to_s.upcase
          button_disabled = false
          button_color = "bg-white text-gray-900 ring-gray-300 hover:bg-gray-50 focus-visible:outline-indigo-600"
        end
      %>
      
      <button type="button"
              class="<%= button_classes %> <%= button_color %> ring-1 ring-inset"
              <%= 'disabled' if button_disabled %>
              <% unless button_disabled %>
                onclick="startTranslation('<%= translatable.class.name %>', '<%= translatable.id %>', '<%= target_locale %>')"
              <% end %>
              <% if existing_job %>
                data-translation-job-id="<%= existing_job.id %>"
                data-status="<%= existing_job.status %>"
              <% end %>>
        
        <% if button_state == 'processing' %>
          <!-- Spinner pro zpracovávající úlohy -->
          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        <% elsif button_state == 'pending' %>
          <!-- Ikona hodinek pro čekající úlohy -->
          <svg class="-ml-1 mr-2 h-4 w-4 text-current" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        <% else %>
          <!-- Ikona překladu pro dostupné úlohy -->
          <svg class="-ml-1 mr-2 h-4 w-4 text-current" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="m10.5 21 5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 0 1 6-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 0 1-3.827-5.802" />
          </svg>
        <% end %>
        
        <%= button_text %>
      </button>
    <% end %>
  </div>
  
  <!-- Skript pro nastavení website_id pro JavaScript -->
  <script>
    window.currentWebsiteId = '<%= @website&.id || params[:website_id] %>';
  </script>
<% end %>
