<!DOCTYPE html>
<html>
<head>
  <title>Admin | <%= current_tenant.name %></title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <%= turbo_refreshes_with method: :replace, scroll: :preserve %>

  <%= yield :head %>
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>

  <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
  <%= javascript_include_tag "admin", "data-turbo-track": "reload", type: "module" %>
  <style>
      .ce-block__content,
      .ce-toolbar__content {
          max-width: unset;
      }
      .codex-editor__redactor {
          padding-bottom: 0 !important;
      }

      .ce-block {
          border: 1px dashed #ed9f05
      }

      .switch {
          background-color: #e5e7eb;
          position: relative;
          display: inline-flex;
          flex-shrink: 0;
          height: 1.5rem;
          width: 2.75rem;
          border: 2px solid transparent;
          border-radius: 9999px;
          cursor: pointer;
          transition: background-color 0.2s ease-in-out;
      }

      .switch:focus {
          outline: none;
      }

      .switch-active > .span-2 {
          background-color: #ec4899;
      }

      .switch-active > .span-3 {
          transform: translateX(1.25rem);
      }

      label {
          display: block;
          margin-bottom: 0.25rem;
          font-size: 0.875rem;
          font-weight: 500;
          color: #111827;
      }

      .label-2 {
          display: block;
          margin-bottom: 0.25rem;
          font-size: 0.875rem;
          font-weight: 600;
          color: #be185d;
      }


      input[type="radio"] {
          accent-color: black; /* Nastaví barvu zaškrtnutí */
          transform: scale(1.2);
      }

      .select:focus {
          outline: 2px solid black;
      }

      #back-link {
          padding: 0.275rem 0.3rem;
          background: none;
          border-radius: 4px;
      }

      #back-link:hover {
          background-color: #f1f1f1;
      }

      .sortable-ghost {
          min-height: 2.5rem;
          width: 100%;
          background-color: #e9e9de;
          list-style-type: none !important;
          border-top: 2px solid #babb9b;
          border-bottom: 2px solid #babb9b;
      }

      .sortable-ghost::marker {
          content: "";
          display: none;
      }

      .language-checkbox {
          position: relative;
      }

      .language-checkbox input:checked + .language-button {
          background-color: #db2777;
          color: white;
      }

      .language-button {
          display: inline-block;
          background-color: #f9fafb;
          padding: 0.5rem;
          border-radius: 0.375rem;
          border: 1px solid #db2777;
          cursor: pointer;
          transition: all 0.3s;
      }

      .language-checkbox input:focus + .language-button {
          outline: 2px solid #60a5fa;
      }

      .pickr button {
          border: 1px solid #e1e1e1 !important;
      }

      .parent-class input:disabled {
          background-color: #f3f4f6;
      }

      .opening-hours-tbody > tr:nth-child(odd) {
          background-color: #f0fdf4;
      }

      h1 {
          font-size: 1.5rem;
          font-weight: 500;
          color: black;
      }

      label .required {
          color: #ef4444;
          margin-left: 0.125rem;
      }

      .calendar table {
          width: 100%;
          border: 1px solid;
      }

      .calendar table th,
      .calendar table td {
          padding: 0.5rem;
          text-align: center;
          background-color: white;
          border: 1px solid;
      }

      #admin-ui .resource .edit-link {
          padding: 0.25rem;
          border-radius: 9999px;
          color: black;
      }

      #admin-ui .resource .edit-link:hover {
          background-color: #e5e7eb;
      }

      #admin-ui .resource .remove-link {
          padding: 0.25rem;
          border-radius: 9999px;
      }

      #admin-ui .resource .remove-link:hover {
          background-color: #fee2e2;
      }

  </style>

  <%= combobox_style_tag %>
</head>

<body data-theme="admin" class="bg-gray-50" id="admin-ui">
<%= turbo_frame_tag :flash do  %>
  <%= render "admin/shared/flash" %>
<% end %>

<div>
  <div class="hidden lg:fixed lg:inset-y-0 lg:z-20 lg:flex lg:w-64 lg:flex-col">
    <div class="flex grow flex-col overflow-y-auto border-r border-gray-200 bg-white">
      <div class="flex h-14 shrink-0 items-center px-6">
        <span class="text-center text-black font-medium text-xl">
          WinWeb
        </span>
      </div>

      <div class="w-full py-2 px-6">
        <div class="relative" data-controller="dropdown" data-action="click@window->dropdown#hide touchstart@window->dropdown#hide keydown.up->dropdown#previousItem keydown.down->dropdown#nextItem keydown.esc->dropdown#hide">
          <button data-action="dropdown#toggle:stop" type="button" class="grid w-full cursor-default grid-cols-1 rounded-md bg-white py-1.5 text-left text-gray-900 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6" aria-haspopup="listbox" aria-expanded="true" aria-labelledby="listbox-label">
            <span class="col-start-1 row-start-1 flex items-center gap-3 pr-6">
              <span aria-label="Online" class="inline-block size-2 shrink-0 rounded-full border border-transparent"></span>
              <span class="block truncate"><%= current_tenant.name %></span>
            </span>
            <svg class="mr-2 col-start-1 row-start-1 size-5 self-center justify-self-end text-gray-500 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
              <path fill-rule="evenodd" d="M5.22 10.22a.75.75 0 0 1 1.06 0L8 11.94l1.72-1.72a.75.75 0 1 1 1.06 1.06l-2.25 2.25a.75.75 0 0 1-1.06 0l-2.25-2.25a.75.75 0 0 1 0-1.06ZM10.78 5.78a.75.75 0 0 1-1.06 0L8 4.06 6.28 5.78a.75.75 0 0 1-1.06-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1 0 1.06Z" clip-rule="evenodd" />
            </svg>
          </button>
          <ul data-dropdown-target="menu" class="hidden absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base ring-1 shadow-lg ring-black/5 focus:outline-hidden sm:text-sm" tabindex="-1" role="listbox" aria-labelledby="listbox-label" aria-activedescendant="listbox-option-3">
            <% current_user.websites.each do |website| %>
              <li  class="relative cursor-pointer text-gray-900 select-none" id="listbox-option-0" role="option">
                <a href="<%= admin_root_path(website) %>" class="w-full px-3 py-2 hover:bg-gray-100 flex items-center">
                  <%= website.name %>
                </a>
              </li>
            <% end %>
          </ul>
        </div>
      </div>

      <nav class="mt-4  px-6 flex flex-1 flex-col">
        <%= admin_menu %>
      </nav>
    </div>
  </div>

  <main class="lg:pl-64">
    <div class="flex px-4 sm:px-3 lg:px-4 justify-between items-center border-b border-gray-200 bg-white">
      <nav class="flex" aria-label="Breadcrumb">
        <ol role="list" class="flex items-center space-x-4 py-4">
          <% breadcrumbs.each_with_index do |crumb, index| %>
            <li>
              <div class="flex items-center">
                <% if index > 0 %>
                  <svg class="h-5 w-5 flex-shrink-0 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                  </svg>
                <% end %>
                <% if crumb.link? %>
                  <%= link_to crumb.name, crumb.path, class: "ml-4 text-base text-gray-500 hover:text-gray-600 #{'text-gray-400 hover:text-gray-500' if index == 0}" %>
                <% else %>
                  <span class="ml-4 text-black" aria-current="page">
                    <%= crumb.name %>
                  </span>
                <% end %>
              </div>
            </li>
          <% end %>
        </ol>
      </nav>
      <div>
        <%= yield :action_toolbar %>
      </div>
    </div>

    <% if content_for?(:sub_navigation) %>
      <div class="flex min-h-screen">
        <div class="w-52 border-r border-gray-200 flex flex-col min-h-full bg-gray-50">
          <%= yield :sub_navigation %>
        </div>

        <div class="flex-1 px-4 sm:px-6 lg:px-8 py-6 lg:py-5">
          <%= yield %>
        </div>
      </div>

    <% else %>
      <div class="px-4 sm:px-6 lg:px-8 py-6 lg:py-5">
        <%= yield %>
      </div>
    <% end %>
  </main>
</div>
</body>
</html>
