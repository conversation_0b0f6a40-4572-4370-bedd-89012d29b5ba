module BlockViews
  class MediaViews::Gallery001Component < BaseComponent
    def view_template
      container do
        inner_container do
          content_container(@block_presenter.controls, css: "mx-auto flex text-center justify-center flex-col")

            media_container do
              render Ui::Gallery.new(
                block_presenter.media_items,
                layout: block_presenter.media.options[:layout] || :grid,
                columns: block_presenter.media.inline_items_count,
                gap: block_presenter.media.options[:gap] || 3,
                resize_options: resize_image_options,
                item_classes: "relative aspect-square overflow-hidden rounded-md"
              )
            end
        end
      end
    end

    private

    def resize_image_options
      { resize_to_fill: [200, 200] }
    end
  end
end
