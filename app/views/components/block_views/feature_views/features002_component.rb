module BlockViews
  class FeatureViews::Features002Component < BaseComponent
    def view_template
      container do
        inner_container do
          content_container(@block_presenter.controls)

          div(class: "bg-accent/20 my-8 h-px")

          media_container do
            div(class: "grid grid-cols-2 sm:divide-x divide-accent/20 space-y-4 sm:space-y-0 md:grid-cols-#{block_presenter.media_options.inline_items_count} gap-#{block_presenter.media_options.gap}") do
              block_presenter.media_items.each do |item|
                div(class: "feature flex flex-col items-center justify-center b") do
                    span(class: "icon rounded-full p-2 bg-accent text-accent-content mb-2") { item.icon.svg_html.html_safe } if item.icon.present?
                    span(class: "text-3xl font-medium") { item.title }
                    p(class: "mt-2 text-xs sm:text-base") { item.text }
                end
              end
            end
          end
        end
      end
    end
  end
end
