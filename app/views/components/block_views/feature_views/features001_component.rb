module BlockViews
  class FeatureViews::Features001Component < BaseComponent
    def view_template
      container do
        inner_container do
          content_container(block_presenter.controls)

            media_container do
              render ::Ui::Features(
                features,
                layout: block_presenter.media_options.layout || :default,
                columns: 3,
                gap: block_presenter.media_options.gap || 3
              )
            end
        end
      end
    end

    private

    def features
      block_presenter.media_items
    end
  end
end
