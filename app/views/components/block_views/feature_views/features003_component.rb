module BlockViews
  class FeatureViews::Features003Component < BaseComponent
    def view_template
      div(class: "py-16", data_theme: "white") do
        div(class: "max-w-4xl mx-auto px-4") do
          h2(class: "h2 text-center text-3xl") { "Proč si vybrat nás?" }
          div(class: "mt-5 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4") do
            div(
              class: "flex flex-col items-center bg-base-100/50 px-3 py-6",
              data_theme: "soft"
            ) do
              span(class: "bg-accent/20 rounded-full p-3") do
                svg(
                  xmlns: "http://www.w3.org/2000/svg",
                  fill: "none",
                  viewbox: "0 0 24 24",
                  stroke_width: "1.5",
                  stroke: "currentColor",
                  class: "size-6"
                ) do |s|
                  s.path(
                    stroke_linecap: "round",
                    stroke_linejoin: "round",
                    d:
                      "M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z"
                  )
                end
              end
              span(
                class: "mt-3 font-medium text-lg h-14 flex items-center text-center"
              ) { "Komplexní péče" }
              p(class: "mt-3 text-sm text-center") do
                "Postaráme se o vás od hlavy až k patě"
              end
            end
            div(
              class: "flex flex-col items-center bg-base-100/50 px-3 py-6",
              data_theme: "soft"
            ) do
              span(class: "bg-accent/20 rounded-full p-3") do
                svg(
                  xmlns: "http://www.w3.org/2000/svg",
                  fill: "none",
                  viewbox: "0 0 24 24",
                  stroke_width: "1.5",
                  stroke: "currentColor",
                  class: "size-6"
                ) do |s|
                  s.path(
                    stroke_linecap: "round",
                    stroke_linejoin: "round",
                    d:
                      "M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z"
                  )
                end
              end
              span(
                class: "mt-3 font-medium text-lg h-14 flex items-center text-center"
              ) { "Dárkové dny na míru" }
              p(class: "mt-3 text-sm text-center") do
                "Vytvoříme perfektní dárek přesně podle vašeho rozpočtu"
              end
            end
            div(
              class: "flex flex-col items-center bg-base-100/50 px-3 py-6",
              data_theme: "soft"
            ) do
              span(class: "bg-accent/20 rounded-full p-3") do
                svg(
                  xmlns: "http://www.w3.org/2000/svg",
                  fill: "none",
                  viewbox: "0 0 24 24",
                  stroke_width: "1.5",
                  stroke: "currentColor",
                  class: "size-6"
                ) do |s|
                  s.path(
                    stroke_linecap: "round",
                    stroke_linejoin: "round",
                    d:
                      "M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"
                  )
                end
              end
              span(
                class: "mt-3 font-medium text-lg h-14 flex items-center text-center"
              ) { "Mladý tým" }
              p(class: "mt-3 text-sm text-center") do
                "Přátelské prostředí s moderním přístupem"
              end
            end
            div(
              class: "flex flex-col items-center bg-base-100/50 px-3 py-6",
              data_theme: "soft"
            ) do
              span(class: "bg-accent/20 rounded-full p-3") do
                svg(
                  xmlns: "http://www.w3.org/2000/svg",
                  fill: "none",
                  viewbox: "0 0 24 24",
                  stroke_width: "1.5",
                  stroke: "currentColor",
                  class: "size-6"
                ) do |s|
                  s.path(
                    stroke_linecap: "round",
                    stroke_linejoin: "round",
                    d:
                      "M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"
                  )
                end
              end
              span(
                class: "mt-3 font-medium text-lg h-14 flex items-center text-center"
              ) { "Příjemné prostředí" }
              p(class: "mt-3 text-sm text-center") do
                "Dobrá káva a relaxační atmosféra"
              end
            end
          end
        end
      end
    end
  end
end
