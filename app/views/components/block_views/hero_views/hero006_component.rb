module BlockViews
  class HeroViews::Hero006Component < BaseComponent
    def view_template
      container do
        overlay
        inner_container do
          div(class:"mx-auto gap-16 md:grid md:grid-cols-2") do
            content_container(@block_presenter.controls, css: "mx-auto flex flex-col mb-6 md:mb-0")

             media_container(css: "flex self-center items-center") do
               Ui::Gallery(
                 block_presenter.media_items,
                 layout: block_presenter.media.options[:layout] || :grid,
                 columns: block_presenter.media.options[:inline_items_count] || 3,
                 gap: block_presenter.media.options[:gap] || 3,
                 resize_options: resize_image_options,
               )
             end
          end
        end
      end
    end

    private

    def resize_image_options
      { resize_to_fill: [300, 400] }
    end
  end
end
