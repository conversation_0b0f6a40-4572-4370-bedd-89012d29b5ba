module BlockControls
  class IconControl < ApplicationComponent
    register_element :block_control
    include Phlex::Rails::Helpers::Tag
    include <PERSON><PERSON><PERSON><PERSON>

    def initialize(button_block_control_object)
      @control = button_block_control_object
    end

    def view_template
      block_control(
        id: %(control-#{@id}),
        ) do
        div(class: "flex space-x-4 items-center") do
          a(
            href: primary_page_link,
            class: "btn btn-accent #{"hidden" if @control.primary_button_text.empty?}",
            id: "control-#{@control.id}-primary-button-text"
          ) { @control.primary_button_text } if admin? || (@control.primary_button_text.present? && primary_page_link)
          a(
            href: secondary_page_link,
            class: "link link-hover text-base-content #{"hidden" if @control.secondary_button_text.empty?}",
            id: "control-#{@control.id}-secondary-button-text"
          ) { @control.secondary_button_text } if admin? || (@control.secondary_button_text.present? && secondary_page_link)
        end
      end
    end

    private

    def primary_page_link
      if @control.primary_button_link_type == "link"
        @control.primary_button_link
      else
        page_path_resolver(Page.find(@control.primary_button_page_id))
      end
    rescue ActiveRecord::RecordNotFound
      nil
    end

    def secondary_page_link
      if @control.secondary_button_link_type == "link"
        @control.secondary_button_link
      else
        page_path_resolver(Page.find(@control.secondary_button_page_id))
      end
    rescue ActiveRecord::RecordNotFound
      nil
    end
  end
end
