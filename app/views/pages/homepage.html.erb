<div>
  <%#= render BlockFactory.create_presenter(source: 'features002', view_context: @view_context).component %>

  <%= render @page_view %>

  <%#= render BlockBuilder.build(@page.blocks.first) %>
</div>

<section class="hidden text-gray-600 body-font relative flex flex-col sm:flex-row">
  <div class="sm:absolute inset-0 bg-gray-300">
    <div data-controller="map" data-map-url="<%= current_tenant.map_url %>" style="min-height: 250px; height: 100%; width: 100%;"></div>
  </div>
  <div class="w-full order-first sm:order-last sm:container sm:px-5 py-5 sm:py-12 mx-auto flex bg-white border">
    <div class="w-full lg:w-1/3 md:w-1/2 p-8 flex flex-col md:ml-auto relative z-10 sm:shadow-md sm:bg-white">
      <span class="text-gray-900 text-lg mb-1 font-medium title-font">Kontakt</span>
      <div class="leading-relaxed mb-5 text-gray-600">
        <strong>Oficina 2.0 - Druhé patro</strong><br>
        <%= current_tenant.address %>
        <br>
        <%= current_tenant.postal_code %> <%= current_tenant.city %> <br />
        tel: <%= current_tenant.phone %>
      </div>

      <span class="text-black text-lg mb-1 font-medium title-font">Otevírací doba</span>
      <% if current_tenant.opening_hours_text.present? %>
        <%= current_tenant.opening_hours_text %>
      <% else %>
        <table class="w-full">
          <tbody>
          <% weekdays.each_with_index do |day, index| %>
            <% date = Date.today.beginning_of_week + index %>

            <%= next if @specific_dates[date].nil? && @generic_days[day[:value]].nil? %>
            <tr class="text-black">
              <td class="<%= Date.today == date ? "font-bold" : "" %>"><%= day[:label] %>:</td>
              <td class="<%= Date.today == date ? "font-bold" : "" %>">
                <% if @specific_dates[date] %>
                  <% @specific_dates[date].each do |opening_hour| %>
                    <p><%= opening_hour.opening_hour.strftime("%H:%M") %> - <%= opening_hour.closing_hour.strftime("%H:%M") %></p>
                  <% end %>
                <% elsif @generic_days[day[:value]] %>
                  <% if @generic_days[day[:value]].opening_hour.present? &&  @generic_days[day[:value]].closing_hour.present? %>
                    <p><%= @generic_days[day[:value]].opening_hour.strftime("%H:%M") %> - <%= @generic_days[day[:value]].closing_hour.strftime("%H:%M") %></p>
                  <% else %>
                    Dle objednání
                  <% end %>
                <% else %>
                  <p>Zavřeno</p>
                <% end %>
              </td>
            </tr>
          <% end %>
          </tbody>
        </table>
      <% end %>
    </div>
  </div>
</section>

<section class="hidden text-gray-600 body-font relative flex flex-col sm:flex-row">
  <div class="sm:absolute inset-0 bg-gray-300">
    <div data-controller="map" data-map-url="<%= current_tenant.map_url %>" style="min-height: 250px; height: 100%; width: 100%;"></div>
  </div>
  <div class="w-full order-first sm:order-last sm:container sm:px-5 py-5 sm:py-12 mx-auto flex bg-white border">
    <div class="w-full lg:w-1/3 md:w-1/2 p-8 flex flex-col md:ml-auto relative z-10 sm:shadow-md sm:bg-white">
      <span class="text-gray-900 text-lg mb-1 font-medium title-font">Kontakt</span>
      <div class="leading-relaxed mb-5 text-gray-600">
        <strong>Oficina 2.0 - Druhé patro</strong><br>
        <%= current_tenant.address %>
        <br>
        <%= current_tenant.postal_code %> <%= current_tenant.city %> <br />
        tel: <%= current_tenant.phone %>
      </div>

      <span class="text-black text-lg mb-1 font-medium title-font">Otevírací doba</span>
      <% if current_tenant.opening_hours_text.present? %>
        <%= current_tenant.opening_hours_text %>
      <% else %>
        <table class="w-full">
          <tbody>
          <% weekdays.each_with_index do |day, index| %>
            <% date = Date.today.beginning_of_week + index %>

            <%= next if @specific_dates[date].nil? && @generic_days[day[:value]].nil? %>
            <tr class="text-black">
              <td class="<%= Date.today == date ? "font-bold" : "" %>"><%= day[:label] %>:</td>
              <td class="<%= Date.today == date ? "font-bold" : "" %>">
                <% if @specific_dates[date] %>
                  <% @specific_dates[date].each do |opening_hour| %>
                    <p><%= opening_hour.opening_hour.strftime("%H:%M") %> - <%= opening_hour.closing_hour.strftime("%H:%M") %></p>
                  <% end %>
                <% elsif @generic_days[day[:value]] %>
                  <% if @generic_days[day[:value]].opening_hour.present? &&  @generic_days[day[:value]].closing_hour.present? %>
                  <p><%= @generic_days[day[:value]].opening_hour.strftime("%H:%M") %> - <%= @generic_days[day[:value]].closing_hour.strftime("%H:%M") %></p>
                    <% else %>
                      Dle objednání
                    <% end %>
                <% else %>
                  <p>Zavřeno</p>
                <% end %>
              </td>
            </tr>
          <% end %>
          </tbody>
        </table>
      <% end %>
    </div>
  </div>
</section>