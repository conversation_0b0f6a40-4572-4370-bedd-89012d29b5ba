module ControlRegistry
  CONTROL_CLASSES = {
    "BlockControls::Paragraph" => Controls::ParagraphPresenter,
    "BlockControls::Heading"   => Controls::HeadingPresenter, # Předpokládáme existenci
    "BlockControls::Button"    => Controls::ButtonPresenter, # Předpokládáme existenci
    "BadgeBlockControlObject"  => Controls::BadgePresenter # Předpokládáme existenci
  }.freeze

  def self.class_for(control_type)
    CONTROL_CLASSES.fetch(control_type) do
      raise "Unknown control type: #{control_type}"
    end
  end
end