class YamlConfigAdapter < ConfigAdapter
  def initialize(identifier)
    @config = BlockConfigLoader.load(identifier)
    @identifier = identifier
  end

  def id
    SecureRandom.hex(3)
  end

  def name
    @config[:name] || @identifier # Pokud váš YAML loader nastaví @config[:name] z nejvyšší úrovně
  end

  def options
    yaml_options_section = (@config[:options] || {}).deep_symbolize_keys

    final_options = {}

    final_options[:outer_container_layer_attributes] = yaml_options_section[:outer_container_layer] || {}
    final_options[:inner_container_layer_attributes] = yaml_options_section[:inner_container_layer] || {}
    final_options[:content_layer_attributes] = yaml_options_section[:content_layer] || {}

    yaml_layer_keys_in_options = [:outer_container_layer, :inner_container_layer, :content_layer]
    global_keys_in_options = yaml_options_section.keys - yaml_layer_keys_in_options

    global_keys_in_options.each do |key|
      final_options[key] = yaml_options_section[key]
    end

    final_options
  end

  def controls_data(locale)
    (@config[:controls] || []).map do |control_hash|
      control_hash.is_a?(Hash) ? control_hash.deep_symbolize_keys : control_hash
    end
  end

  def media_items
    return nil if @config[:media_items].nil?

    media_items = []
    @config[:media_items].each do |media_item|
      media_items << MediaItemData.new(
        id: SecureRandom.hex(3),
        title: media_item[:title],
        text: media_item[:text],
        published_at: media_item[:published_at],
        image_url: media_item[:image_url],
        **media_item[:custom_fields] || {}
      )
    end

    media_items
  end

  def media_options
    MediaOptionsData.new(
      alignment: @config[:media_options][:alignment],
      inline_items_count: @config[:media_options][:inline_items_count],
      posts_limit: @config[:media_options][:posts_limit],
      position: @config[:media_options][:position],
      gap: @config[:media_options][:gap],
      type: @config[:media_options][:type],
      layout: @config[:media_options][:layout]
    )
  end

  def pricing_id
    @config[:pricing_id] # Vrací nil, pokud není v @config
  end

  def pricing_options
    (@config[:pricing_options] || {}).deep_symbolize_keys
  end

  def background_image_attachment
    nil # YAML typicky neobsahuje přímé ActiveStorage přílohy
  end

  def background_image_mobile_attachment
    nil # YAML typicky neobsahuje přímé ActiveStorage přílohy
  end
end