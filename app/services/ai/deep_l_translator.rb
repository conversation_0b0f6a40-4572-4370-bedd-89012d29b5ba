module Ai
  class DeepLTranslator
    API_URL = "https://api-free.deepl.com/v2/translate".freeze
    API_KEY = "1f0750d7-da68-4a86-a584-085381cf5d22:fx".freeze
    
    # Mapování Rails locale na DeepL jazykové kódy
    LOCALE_MAPPING = {
      'cs' => 'CS',
      'sk' => 'SK', 
      'en' => 'EN-US'
    }.freeze
    
    class TranslationError < StandardError; end
    class UnsupportedLocaleError < TranslationError; end
    class APIError < TranslationError; end
    
    def initialize
      @connection = Faraday.new(url: API_URL) do |conn|
        conn.request :url_encoded
        conn.response :json, content_type: /\bjson$/
        conn.adapter Faraday.default_adapter
      end
    end
    
    # Hlavní metoda pro překlad obsahu
    # @param content [Hash] Hash s klíči a texty k přeložení
    # @param source_locale [String] Zdrojo<PERSON><PERSON> jazyk (např. 'cs')
    # @param target_locale [String] <PERSON><PERSON><PERSON><PERSON> jazyk (např. 'en')
    # @return [Hash] Hash se stejnými klíči a přeloženými texty
    def call(content:, source_locale:, target_locale:)
      validate_locales!(source_locale, target_locale)
      
      # Pokud je obsah prázdný, vrátíme prázdný hash
      return {} if content.blank?
      
      # Extrahujeme texty k přeložení
      texts_to_translate = extract_translatable_texts(content)
      
      # Pokud nejsou žádné texty k přeložení, vrátíme původní obsah
      return content if texts_to_translate.empty?
      
      # Přeložíme texty
      Rails.logger.info "DeepL: Translating #{texts_to_translate.length} texts: #{texts_to_translate.inspect}"

      translated_texts = translate_texts(
        texts_to_translate,
        source_locale,
        target_locale
      )
      
      # Sestavíme výsledný hash s přeloženými texty
      build_translated_content(content, translated_texts)
    rescue Faraday::Error => e
      raise APIError, "DeepL API error: #{e.message}"
    rescue => e
      raise TranslationError, "Translation failed: #{e.message}"
    end
    
    private
    
    def validate_locales!(source_locale, target_locale)
      unless LOCALE_MAPPING.key?(source_locale)
        raise UnsupportedLocaleError, "Unsupported source locale: #{source_locale}"
      end
      
      unless LOCALE_MAPPING.key?(target_locale)
        raise UnsupportedLocaleError, "Unsupported target locale: #{target_locale}"
      end
    end
    
    # Extrahuje všechny textové hodnoty z hash struktury
    def extract_translatable_texts(content)
      texts = []
      extract_texts_recursive(content, texts)
      texts.compact.reject(&:blank?)
    end
    
    def extract_texts_recursive(obj, texts)
      case obj
      when Hash
        obj.each_value { |value| extract_texts_recursive(value, texts) }
      when Array
        obj.each { |item| extract_texts_recursive(item, texts) }
      when String
        texts << obj if translatable_text?(obj)
      end
    end
    
    # Kontroluje, zda je text vhodný k přeložení
    def translatable_text?(text)
      return false if text.blank?
      return false if text.length < 2
      return false if text.match?(/\A\s*\z/) # pouze whitespace
      return false if text.match?(/\A[0-9\s\-\+\(\)\.]+\z/) # pouze čísla a znaky
      return false if text.match?(/\A[a-zA-Z0-9\-\_\.@]+\z/) && text.include?('@') # email
      return false if text.match?(/\Ahttps?:\/\//) # URL
      
      true
    end
    
    # Přeloží pole textů pomocí DeepL API
    def translate_texts(texts, source_locale, target_locale)
      return [] if texts.empty?

      # Filtrujeme prázdné texty
      valid_texts = texts.reject(&:blank?)
      return [] if valid_texts.empty?

      Rails.logger.info "DeepL API: Sending #{valid_texts.length} texts to translate"

      source_lang = LOCALE_MAPPING[source_locale]
      target_lang = LOCALE_MAPPING[target_locale]

      binding.irb
      response = @connection.post do |req|
        req.headers['Authorization'] = "DeepL-Auth-Key #{API_KEY}"
        req.body = {
          text: valid_texts.first,
          source_lang: source_lang,
          target_lang: target_lang,
          preserve_formatting: true,
          tag_handling: 'html'
        }
      end
      
      handle_api_response(response)
    end
    
    def handle_api_response(response)
      unless response.success?
        error_message = response.body.is_a?(Hash) ? response.body['message'] : response.body
        raise APIError, "DeepL API returned #{response.status}: #{error_message}"
      end
      
      translations = response.body['translations']
      unless translations.is_a?(Array)
        raise APIError, "Unexpected API response format"
      end
      
      translations.map { |t| t['text'] }
    end
    
    # Sestaví výsledný hash s přeloženými texty
    def build_translated_content(original_content, translated_texts)
      text_index = 0
      result = deep_dup(original_content)
      
      replace_texts_recursive(result, translated_texts, text_index)
      result
    end
    
    def replace_texts_recursive(obj, translated_texts, text_index)
      case obj
      when Hash
        obj.each do |key, value|
          obj[key] = replace_texts_recursive(value, translated_texts, text_index)
        end
      when Array
        obj.map! { |item| replace_texts_recursive(item, translated_texts, text_index) }
      when String
        if translatable_text?(obj) && text_index[0] < translated_texts.length
          result = translated_texts[text_index[0]]
          text_index[0] += 1
          result
        else
          obj
        end
      else
        obj
      end
    end
    
    def deep_dup(obj)
      case obj
      when Hash
        obj.transform_values { |value| deep_dup(value) }
      when Array
        obj.map { |item| deep_dup(item) }
      else
        obj.dup rescue obj
      end
    end
  end
end
