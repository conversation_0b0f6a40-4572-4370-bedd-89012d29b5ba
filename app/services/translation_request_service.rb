class TranslationRequestService
  class DuplicateJobError < StandardError; end
  class UnsupportedObjectError < StandardError; end
  
  # Podporované typy objektů pro překlad
  SUPPORTED_TYPES = %w[MediaContent].freeze
  
  def initialize(translatable_object, target_locale)
    @translatable_object = translatable_object
    @target_locale = target_locale.to_s
    @source_locale = determine_source_locale
    
    validate_inputs!
  end
  
  # Hlavní metoda pro vytvoření a zařazení překladatelsk<PERSON> úlohy
  def call
    # Kontrola duplicitní <PERSON>
    check_for_duplicate_job!
    
    # Extrakce obsahu k přeložení
    source_content = extract_source_content
    
    # Vytvoření záznamu v databázi
    translation_job = create_translation_job(source_content)
    
    # Zařazení úlohy do fronty
    TranslationWorkerJob.perform_later(translation_job.id)
    
    translation_job
  rescue => e
    Rails.logger.error "TranslationRequestService failed: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    raise
  end
  
  private

  def supported_object?(object)
    # MediaContent je pří<PERSON> podpor<PERSON>ý
    return true if SUPPORTED_TYPES.include?(object.class.name)

    # BlockControl a všechny jeho podtřídy (BlockControls::Heading, atd.)
    return true if object.is_a?(BlockControl)

    false
  end

  def validate_inputs!
    unless @translatable_object
      raise ArgumentError, "Translatable object cannot be nil"
    end

    # Kontrola, zda je objekt podporovaný typ
    unless supported_object?(@translatable_object)
      raise UnsupportedObjectError,
            "Object type #{@translatable_object.class.name} is not supported for translation"
    end
    
    unless I18n.available_locales.map(&:to_s).include?(@target_locale)
      raise ArgumentError, "Target locale '#{@target_locale}' is not supported"
    end
    
    if @source_locale == @target_locale
      raise ArgumentError, "Source and target locales cannot be the same"
    end
  end
  
  def determine_source_locale
    if @translatable_object.respond_to?(:locale)
      @translatable_object.locale
    else
      raise UnsupportedObjectError, "Cannot determine source locale for #{@translatable_object.class.name}"
    end
  end
  
  def check_for_duplicate_job!
    existing_job = TranslationJob.where(
      translatable: @translatable_object,
      target_locale: @target_locale
    ).active.first
    
    if existing_job
      raise DuplicateJobError, 
            "Translation job already exists (ID: #{existing_job.id}, Status: #{existing_job.status})"
    end
  end
  
  def extract_source_content
    if @translatable_object.is_a?(MediaContent)
      extract_media_content
    elsif @translatable_object.is_a?(BlockControl)
      extract_block_control_content
    else
      raise UnsupportedObjectError, "Cannot extract content from #{@translatable_object.class.name}"
    end
  end
  
  def extract_media_content
    content = {}
    
    # Pevné sloupce
    content[:title] = @translatable_object.title if @translatable_object.title.present?
    content[:text] = @translatable_object.text if @translatable_object.text.present?
    content[:content] = @translatable_object.content if @translatable_object.content.present?
    content[:caption] = @translatable_object.caption if @translatable_object.caption.present?
    
    # Custom fields
    if @translatable_object.custom_fields.present?
      @translatable_object.custom_fields.each do |key, value|
        if value.is_a?(String) && value.present?
          content["custom_fields.#{key}"] = value
        end
      end
    end
    
    content
  end
  
  def extract_block_control_content
    content = {}
    
    # Text obsah
    content[:text] = @translatable_object.text if @translatable_object.text.present?
    
    # Options - extrahujeme textové hodnoty
    if @translatable_object.options.present?
      extract_text_from_hash(@translatable_object.options, content, 'options')
    end
    
    # Content - extrahujeme textové hodnoty
    if @translatable_object.content.present?
      extract_text_from_hash(@translatable_object.content, content, 'content')
    end
    
    content
  end
  
  def extract_text_from_hash(hash, content, prefix)
    hash.each do |key, value|
      case value
      when String
        content["#{prefix}.#{key}"] = value if value.present?
      when Hash
        extract_text_from_hash(value, content, "#{prefix}.#{key}")
      when Array
        value.each_with_index do |item, index|
          if item.is_a?(Hash)
            extract_text_from_hash(item, content, "#{prefix}.#{key}.#{index}")
          elsif item.is_a?(String) && item.present?
            content["#{prefix}.#{key}.#{index}"] = item
          end
        end
      end
    end
  end
  
  def create_translation_job(source_content)
    TranslationJob.create!(
      translatable: @translatable_object,
      source_locale: @source_locale,
      target_locale: @target_locale,
      source_content: source_content,
      status: :pending
    )
  end
end
