class MediaOptionsData
  attr_accessor :inline_items_count, :posts_limit, :position, :gap, :type, :layout

  def initialize(inline_items_count:, posts_limit:, position:, gap:, type:, layout:)
    @inline_items_count = inline_items_count
    @posts_limit = posts_limit
    @position = position
    @gap = gap
    @type = type
    @layout = layout
  end

  def to_hash
    {
      inline_items_count: @inline_items_count,
      posts_limit: @posts_limit,
      position: @position,
      gap: @gap,
      type: @type,
      layout: @layout
    }
  end
end