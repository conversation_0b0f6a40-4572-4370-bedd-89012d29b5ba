class MediaItemData
  attr_accessor :id, :title, :text, :icon, :published_at, :image_attachment, :image_url, :custom_fields

  def initialize(id: nil, title: nil, text: nil, icon: nil, published_at: nil, image_url: nil, image_attachment: nil, **custom_fields)
    @id = id
    @title = title
    @published_at = published_at
    @text = text
    @icon = icon
    @image_url = image_url
    @image_attachment = image_attachment
    @custom_fields = custom_fields
  end
end
