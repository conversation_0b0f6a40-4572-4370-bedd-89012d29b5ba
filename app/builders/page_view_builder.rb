class PageViewBuilder
  attr_reader :page_presenter

  def initialize(page_presenter)
    @page_presenter = page_presenter
  end

  def render_in(view_context, &block)
    @page_presenter.blocks.map do |component|
      component.render_in(view_context)
    end.join.html_safe
  end

  def self.build(page:, context:)
    models = PageCompositionService.new(page).call(scope: :full)

    components = models.map { |model| BlockFactory.create_presenter(source: model, view_context: context).component }.compact

    page_presenter = PagePresenter.new(
      name: page.title,
      blocks: components
    )

    new(page_presenter)
  end
end