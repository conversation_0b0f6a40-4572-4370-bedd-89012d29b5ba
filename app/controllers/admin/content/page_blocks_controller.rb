# frozen_string_literal: true

module Admin
  class Content::PageBlocksController < Content::ContentController
    include BlockManagement
    include PageContextBuilder

    layout "editor"

    before_action :set_page
    before_action :set_block, only: [:edit, :update, :hide, :destroy, :sort]

    def index
      @blocks = PageCompositionService.new(@page).call(scope: :content_only)

      build_view_context(type: :admin)

      @block_components = @blocks.each_with_object({}) do |block_model, hash|
        hash[block_model.id] = BlockFactory.create_presenter(source: block_model, view_context: @view_context).component
      end
    end

    def update_media
      build_view_context(type: :admin)

      block = Block.find(params[:id])

      if block.update(media_params)
        @block_presenter = BlockFactory::create_presenter(source: block, view_context: @view_context)

        @response = @block_presenter.component.call(fragments: ["fragment_media_wrapper"])
      else
        redirect_to edit_admin_content_page_block_path(@page, block)
      end
    end

    def edit
      @controls = @block.controls_for_locale(@page.locale)
    end

    def new
      @block = Block.new

      if params[:add].present?
        block_type = params[:add].to_sym

        build_view_context(type: :admin)

        if block_type == :references
          # Načteme existující bloky pro výběr reference
          existing_blocks = Block.joins(:page_blocks)
                                 .where.not(page_blocks: { page_id: @page.id })
                                 .distinct
                                 .includes(:controls, :media_collection)

          @block_components = existing_blocks.map { |block| BlockFactory.create_presenter(source: block, view_context: @view_context).component }.compact
        else
          @block_components = BlockFactory.build_by_type(block_type, view_context: @view_context)
        end
      end
    end

    def create
        build_view_context(type: :admin)

        block_data = BlockFactory::build_block_data(source: params.dig(:block_type), locale: @page.locale)

        new_block = BlockCreator::build(block_data: block_data, website: Current.website)

        @page.blocks << new_block

        if new_block
          page_block = PageBlock.where(block: new_block, page: @page).take
          if params[:after].present?
            after_page_block = @page.page_blocks.find_by(block_id: params[:after])
            page_block.update(position: { after: after_page_block }) if after_page_block
          elsif params[:before].present?
            before_page_block = @page.page_blocks.find_by(block_id: params[:before])
            page_block.update(position: { before: before_page_block }) if before_page_block
          end
        end

        redirect_to admin_content_page_blocks_path(@page)
    end

    def create_reference
      reference_block = Block.find(params[:block_id])

      # Vytvoříme nový PageBlock záznam pro referenci
      page_block = @page.page_blocks.build(block: reference_block)

      if page_block.save
        # Nastavíme pozici po uložení
        if params[:after].present?
          after_page_block = @page.page_blocks.find_by(block_id: params[:after])
          page_block.update(position: { after: after_page_block }) if after_page_block
        elsif params[:before].present?
          before_page_block = @page.page_blocks.find_by(block_id: params[:before])
          page_block.update(position: { before: before_page_block }) if before_page_block
        end

        # Zajistíme, že existují BlockControls pro aktuální lokalizaci stránky
        ensure_block_controls_for_locale(reference_block, @page.locale)
        redirect_to admin_content_page_blocks_path(@page)
      else
        redirect_to admin_content_page_blocks_path(@page), alert: "Nepodařilo se přidat referenci bloku."
      end
    end

    def sort
      page_block = @page.page_blocks.find_by(block: @block)
      unless page_block
        redirect_to after_update_path, alert: "Vazba mezi stránkou a blokem nenalezena."
        return
      end

      case params[:direction]
      when "up"
        prev_item = page_block.previous_item
        page_block.update(position: { before: prev_item }) if prev_item
      when "down"
        next_item = page_block.next_item
        page_block.update(position: { after: next_item }) if next_item
      end
      redirect_to after_update_path, notice: "Pořadí bloků bylo změněno."
    end

    private

    def set_page
      @page = Page.friendly.find(params[:page_id])
      @owner ||= @page
    end

    def set_block
      @block = @page.blocks.find(params[:id])
    end

    def ensure_block_controls_for_locale(block, locale)
      # Zkontrolujeme, zda existují controls pro danou lokalizaci
      existing_controls = block.controls.for_locale(locale)

      if existing_controls.empty?
        # Najdeme controls z jiné lokalizace jako šablonu
        template_controls = block.controls.where.not(locale: locale).group_by(&:position)

        template_controls.each do |position, controls|
          # Vezmeme první control z této pozice jako šablonu
          template_control = controls.first

          # Vytvoříme nový control pro požadovanou lokalizaci
          block.controls.create!(
            type: template_control.type,
            position: position,
            locale: locale,
            text: template_control.text,
            options: template_control.options,
            classes: template_control.classes,
            styles: template_control.styles,
            content: template_control.content,
            container: template_control.container
          )
        end
      end
    end

    def media_params
      params.require(:block).permit(
        :media_id,
        :media_collection_id,
        :media_inline_items_count, :media_posts_limit, :media_gallery_type, :media_position,
        media: [ :id, images: [] ]
      )
    end

    def after_update_path
      admin_content_page_blocks_path(@page)
    end
  end
end
