module Admin
  class Content::MediaController < Content::ContentController
    include PageContextBuilder

    before_action :set_block, only: [:index, :new, :create, :edit, :update]
    before_action :set_medium, only: [:edit, :update, :destroy]

    def index
      @media = Media.all
    end

    def new
      @medium = Media.new(media_type: @block.media_type)

      if params[:page_id].present?
        @page = Page.find(params[:page_id])
      end
    end

    def edit
      if params[:page_id].present?
        @page = Page.find(params[:page_id])
      end
    end

    def create
      if params[:media][:page_id].present?
        @page = Page.find(params[:media][:page_id])
      end

      if @block.media_collection.nil?
        # Získáme media_type_id z parametrů nebo z bloku
        media_type_id = params[:media][:media_type_id]

        # Pokud není media_type_id v parametrech, zkusíme ho získat z bloku
        if media_type_id.blank? && @block.media_type.present?
          media_type = MediaType.find_by(name: @block.media_type)
          media_type_id = media_type.id if media_type
        end

        media_collection = MediaCollection.create(
          website: current_tenant,
          name: @block.block_name,
          media_type_id: media_type_id
        )

        @block.update(media_collection: media_collection)
      else
        media_collection = @block.media_collection

        # Aktualizujeme media_type_id pro media_collection, pokud ještě není nastaven
        if media_collection.media_type_id.nil? && params[:medium][:media_type_id].present?
          media_collection.update(media_type_id: params[:medium][:media_type_id])
        end
      end

      @medium = Media.new(medium_params)
      @medium.media_collection = media_collection
      @medium.website = current_tenant

      if @medium.save
        build_view_context(type: :admin)

        @block_presenter = BlockFactory::create_presenter(source: @block, view_context: @view_context)

        @response = @block_presenter.component.call(fragments: ["fragment_media_wrapper"])
      else
        render :new, status: :unprocessable_entity
      end
    end

    def update
      if params[:media][:page_id].present?
        @page = Page.find(params[:media][:page_id])
      end

      if @medium.update(medium_params)
        build_view_context(type: :admin)

        @block_presenter = BlockFactory::create_presenter(source: @block, view_context: @view_context)

        @response = @block_presenter.component.call(fragments: ["fragment_media_wrapper"])
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def sort
      build_view_context(type: :admin)

      @media = Media.find(params[:id])
      @media.update position: params[:position].to_i
      block = Block.find(params[:block_id])

      @block_presenter = BlockFactory::create_presenter(source: block, view_context: @view_context)

      @response = @block_presenter.component.call(fragments: ["fragment_media_wrapper"])
    end

    def destroy
      if params[:page_id].present?
        @page = Page.find(params[:page_id])
      end

      build_view_context(type: :admin)

      @block = Block.find(params[:block_id])
      @medium.destroy

      @block_presenter = BlockFactory::create_presenter(source: @block, view_context: @view_context)

      @response = @block_presenter.component.call(fragments: ["fragment_media_wrapper"])
    end

    private

    def set_block
      @block = Block.find(params[:block_id])
    end

    def set_medium
      @medium = Media.find(params[:id])
    end

    def medium_params
      params.require(:media).permit(
        :media_type_id, :image, :icon_id, :author, :published_at,
        data: {},
        contents_attributes: [
          :id, :locale, :title, :text, :content, :caption,
          custom_fields: {}
        ]
      )
    end
  end
end
