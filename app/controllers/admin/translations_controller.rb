class Admin::TranslationsController < Admin::ApplicationController
  before_action :set_breadcrumbs
  
  def index
    page = [params[:page].to_i, 1].max
    @translation_jobs = TranslationJob.order(created_at: :desc)
                                     .limit(20)
                                     .offset((page - 1) * 20)
    
    # Filtrování podle stavu
    if params[:status].present? && TranslationJob.statuses.key?(params[:status])
      @translation_jobs = @translation_jobs.where(status: params[:status])
    end
    
    # Filtrování podle typu objektu
    if params[:translatable_type].present?
      @translation_jobs = @translation_jobs.where(translatable_type: params[:translatable_type])
    end
  end
  
  def show
    @translation_job = TranslationJob.find(params[:id])
    add_breadcrumb "Úloha ##{@translation_job.id}"
  end
  
  def create
    # Překlad celého bloku (všech jeho controls)
    translate_block
  end
  
  def retry
    @translation_job = TranslationJob.find(params[:id])

    if @translation_job.status_failed?
      # Resetujeme stav a za<PERSON><PERSON><PERSON><PERSON> z<PERSON>u do fronty
      @translation_job.update!(
        status: :pending,
        error_message: nil,
        processed_at: nil
      )

      TranslationWorkerJob.perform_later(@translation_job.id)

      redirect_to admin_translation_path(@translation_job),
                  notice: "Překlad byl zařazen znovu do fronty"
    else
      redirect_to admin_translation_path(@translation_job),
                  alert: "Pouze neúspěšné překlady lze opakovat"
    end
  end
  
  def destroy
    @translation_job = TranslationJob.find(params[:id])
    
    if @translation_job.active?
      render json: {
        success: false,
        message: "Nelze smazat aktivní úlohu"
      }, status: :unprocessable_entity
    else
      @translation_job.destroy!
      
      render json: {
        success: true,
        message: "Úloha byla smazána"
      }
    end
  end
  
  # API endpoint pro kontrolu stavu úlohy
  def status
    @translation_job = TranslationJob.find(params[:id])
    
    render json: {
      id: @translation_job.id,
      status: @translation_job.status,
      description: @translation_job.description,
      created_at: @translation_job.created_at,
      processed_at: @translation_job.processed_at,
      error_message: @translation_job.error_message,
      processing_duration: @translation_job.processing_duration
    }
  end
  
  private

  def translate_block
    block = Block.find(params[:block_id])
    source_locale = params[:source_locale] || @website.locale

    # Automaticky určíme cílový jazyk - první dostupný jazyk, který není zdrojový
    available_locales = @website.available_locales
    target_locale = available_locales.find { |locale| locale != source_locale }

    if target_locale.nil?
      redirect_back(fallback_location: admin_root_path(@website),
                    alert: "Není dostupný žádný cílový jazyk pro překlad")
      return
    end

    # Najdeme všechny controls pro zdrojový jazyk
    source_controls = block.controls_for_locale(source_locale)

    if source_controls.empty?
      redirect_back(fallback_location: admin_root_path(@website),
                    alert: "Blok nemá žádný obsah v jazyce #{source_locale.upcase}")
      return
    end

    translation_jobs = []

    begin
      source_controls.each do |control|
        service = TranslationRequestService.new(control, target_locale)
        translation_jobs << service.call
      end

      redirect_back(fallback_location: admin_root_path(@website),
                    notice: "Překlad bloku byl zařazen do fronty (#{translation_jobs.count} úloh) - #{source_locale.upcase} → #{target_locale.upcase}")
    rescue => e
      Rails.logger.error "Block translation failed: #{e.message}"

      redirect_back(fallback_location: admin_root_path(@website),
                    alert: "Nastala chyba při vytváření překladu bloku: #{e.message}")
    end
  end


  
  def set_breadcrumbs
    add_breadcrumb "Překlady", (admin_translations_path if action_name != "index")
  end
end
