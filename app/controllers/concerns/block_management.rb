# frozen_string_literal: true

  module BlockManagement
    extend ActiveSupport::Concern

    def update
      if @block.update(block_params)
        redirect_to after_update_path
      else
        # Zde bychom měli s<PERSON>ně renderovat 'edit' s chybami
        render :edit, status: :unprocessable_entity
      end
    end

    def hide
      @block.update(hidden_at: @block.hidden? ? nil : Time.now)
      redirect_to after_update_path
    end

    def destroy
      @block.destroy
      redirect_to after_update_path
    end

    private

    # Přesunuli jsme strong params sem, protože jsou pro všechny bloky stejné.
    def block_params
      params.require(:block).permit(
        :background_image, :height, :pricing_id, :media_collection_id,
        options: {}, pricing_options: {},
        outer_container_layer_attributes: Block::OuterContainerLayer.attribute_names.map(&:to_sym),
        inner_container_layer_attributes: Block::InnerContainerLayer.attribute_names.map(&:to_sym),
        content_layer_attributes: Block::ContentLayer.attribute_names.map(&:to_sym),
      ).tap do |whitelisted|
        if params.dig(:block, :controls_attributes)
          whitelisted[:controls_attributes] = block_controls_params
        end
      end
    end

    def block_controls_params
      permitted_controls = {}
      params[:block][:controls_attributes].each do |key, attributes|
        type = attributes[:type]
        klass = type.safe_constantize

        permitted_attributes = if klass && klass < BlockControl
                                 klass.permitted_attributes + [:id, :locale, :_destroy]
                               else
                                 [:id, :locale, :_destroy]
                               end
        permitted_controls[key] = attributes.permit(*permitted_attributes)
      end
      permitted_controls
    end
  end

