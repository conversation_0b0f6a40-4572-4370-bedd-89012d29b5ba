class CreateMediaContents < ActiveRecord::Migration[8.0]
  def change
    create_table :media_contents do |t|
      t.references :media, null: false, foreign_key: true
      t.string :locale, null: false

      # === <PERSON><PERSON><PERSON><PERSON>, společn<PERSON> sloupce pro výkon a snadné dotazování ===
      t.string :title
      t.text :text
      t.text :content # Pro ActionText, pokud používáte
      t.string :caption

      # === JSONB sloupec pro všechna ostatní, dynamická pole ===
      t.jsonb :custom_fields, null: false, default: {}

      t.timestamps
    end

    # Unikátní index zajistí, že pro jedno médium a jeden jazyk existuje pouze jeden záznam.
    add_index :media_contents, [:media_id, :locale], unique: true
  end
end
