class CreateTranslationJobs < ActiveRecord::Migration[8.0]
  def change
    create_table :translation_jobs do |t|
      # Polymorfní reference na objekt k přeložení
      t.references :translatable, polymorphic: true, null: false, index: true
      
      # Jazyky
      t.string :source_locale, null: false
      t.string :target_locale, null: false
      
      # Obsah k přeložení (kopie v momentě vytvoření úlohy)
      t.jsonb :source_content, null: false, default: {}
      
      # Přeložený obsah vrácený od AI (pro logování)
      t.jsonb :translated_content, null: false, default: {}
      
      # Stav úlohy
      t.integer :status, null: false, default: 0
      
      # Chybová zpráva v případě selhání
      t.text :error_message
      
      # Časové razítko dokončení
      t.datetime :processed_at
      
      t.timestamps
    end
    
    # Index pro rychlé vyhledávání podle stavu
    add_index :translation_jobs, :status
    
    # Index pro vyhledávání podle kombinace objektu a cílového jazyka
    add_index :translation_jobs, [:translatable_type, :translatable_id, :target_locale], 
              name: 'index_translation_jobs_on_translatable_and_target_locale'
    
    # Index pro vyhledávání podle časového razítka
    add_index :translation_jobs, :processed_at
  end
end
