class RemoveContentColumnsFromMedia < ActiveRecord::Migration[8.0]
  def change
    # POZOR: Spusťte tuto migraci až po ověření, že MediaContent systém funguje správně!

    # Odstraníme redundantní sloupce z tabulky media
    remove_column :media, :title, :string
    remove_column :media, :text, :text
    remove_column :media, :content, :text

    # Zvažte i odstranění sloupce `data`, pokud už ho nepotřebujete pro jiné ú<PERSON>ely
    # remove_column :media, :data, :jsonb
  end
end
