# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_06_16_125053) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "hstore"
  enable_extension "pg_catalog.plpgsql"

  create_table "account_users", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_account_users_on_account_id"
    t.index ["user_id"], name: "index_account_users_on_user_id"
  end

  create_table "accounts", force: :cascade do |t|
    t.string "name"
    t.string "billing_details"
    t.string "billing_email"
    t.string "domain"
    t.string "subdomain"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "action_text_rich_texts", force: :cascade do |t|
    t.string "name", null: false
    t.text "body"
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["record_type", "record_id", "name"], name: "index_action_text_rich_texts_uniqueness", unique: true
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "block_controls", force: :cascade do |t|
    t.string "type"
    t.text "text"
    t.jsonb "styles"
    t.jsonb "options"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position", default: 0
    t.jsonb "classes", default: {}
    t.jsonb "content", default: {}
    t.bigint "block_id"
    t.string "container"
    t.string "locale", default: "cs", null: false
    t.index ["block_id", "locale"], name: "index_block_controls_on_block_id_and_locale"
    t.index ["block_id"], name: "index_block_controls_on_block_id"
  end

  create_table "blocks", force: :cascade do |t|
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "hidden_at"
    t.jsonb "options", default: {}
    t.jsonb "media_options", default: {}
    t.string "name"
    t.jsonb "pricing_options", default: {}
    t.bigint "pricing_id"
    t.bigint "media_collection_id"
    t.bigint "media_type_id"
    t.index ["media_collection_id"], name: "index_blocks_on_media_collection_id"
    t.index ["media_type_id"], name: "index_blocks_on_media_type_id"
    t.index ["pricing_id"], name: "index_blocks_on_pricing_id"
  end

  create_table "forms", force: :cascade do |t|
    t.string "name"
    t.integer "form_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "website_id", null: false
    t.boolean "enabled", default: true
    t.jsonb "options", default: {}
    t.index ["website_id"], name: "index_forms_on_website_id"
  end

  create_table "friendly_id_slugs", force: :cascade do |t|
    t.string "slug", null: false
    t.integer "sluggable_id", null: false
    t.string "sluggable_type", limit: 50
    t.string "scope"
    t.datetime "created_at"
    t.index ["slug", "sluggable_type", "scope"], name: "index_friendly_id_slugs_on_slug_and_sluggable_type_and_scope", unique: true
    t.index ["slug", "sluggable_type"], name: "index_friendly_id_slugs_on_slug_and_sluggable_type"
    t.index ["sluggable_type", "sluggable_id"], name: "index_friendly_id_slugs_on_sluggable_type_and_sluggable_id"
  end

  create_table "html_tags", force: :cascade do |t|
    t.string "name"
    t.text "content"
    t.integer "position"
    t.boolean "active"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "page_id", null: false
  end

  create_table "icons", force: :cascade do |t|
    t.text "svg_html"
    t.text "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "images", force: :cascade do |t|
    t.bigint "website_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "image_url"
    t.index ["website_id"], name: "index_images_on_website_id"
  end

  create_table "inbox_messages", force: :cascade do |t|
    t.bigint "form_id", null: false
    t.text "message"
    t.string "email"
    t.string "phone"
    t.string "name"
    t.integer "status", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "website_id", null: false
    t.index ["form_id"], name: "index_inbox_messages_on_form_id"
    t.index ["website_id"], name: "index_inbox_messages_on_website_id"
  end

  create_table "layout_blocks", force: :cascade do |t|
    t.bigint "layout_id", null: false
    t.bigint "block_id", null: false
    t.integer "position"
    t.string "location"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["block_id"], name: "index_layout_blocks_on_block_id"
    t.index ["layout_id"], name: "index_layout_blocks_on_layout_id"
  end

  create_table "layouts", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "main_header_block_id"
    t.bigint "main_footer_block_id"
    t.bigint "website_id", null: false
    t.index ["main_footer_block_id"], name: "index_layouts_on_main_footer_block_id"
    t.index ["main_header_block_id"], name: "index_layouts_on_main_header_block_id"
    t.index ["website_id"], name: "index_layouts_on_website_id"
  end

  create_table "media", force: :cascade do |t|
    t.bigint "website_id", null: false
    t.string "unique_id"
    t.datetime "published_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "origin"
    t.bigint "icon_id"
    t.bigint "media_collection_id"
    t.integer "position", default: 1, null: false
    t.bigint "media_type_id"
    t.index ["icon_id"], name: "index_media_on_icon_id"
    t.index ["media_collection_id"], name: "index_media_on_media_collection_id"
    t.index ["media_type_id"], name: "index_media_on_media_type_id"
    t.index ["website_id"], name: "index_media_on_website_id"
  end

  create_table "media_collections", force: :cascade do |t|
    t.bigint "website_id", null: false
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "collection_type"
    t.string "source"
    t.bigint "media_type_id", null: false
    t.index ["media_type_id"], name: "index_media_collections_on_media_type_id"
    t.index ["website_id"], name: "index_media_collections_on_website_id"
  end

  create_table "media_contents", force: :cascade do |t|
    t.bigint "media_id", null: false
    t.string "locale", null: false
    t.string "title"
    t.text "text"
    t.text "content"
    t.string "caption"
    t.jsonb "custom_fields", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["media_id", "locale"], name: "index_media_contents_on_media_id_and_locale", unique: true
    t.index ["media_id"], name: "index_media_contents_on_media_id"
  end

  create_table "media_fields", force: :cascade do |t|
    t.bigint "media_type_id", null: false
    t.string "field_key"
    t.string "field_type"
    t.integer "position"
    t.boolean "required"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["media_type_id"], name: "index_media_fields_on_media_type_id"
  end

  create_table "media_types", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "slug"
  end

  create_table "opening_hours", force: :cascade do |t|
    t.bigint "website_id", null: false
    t.integer "day", default: 0
    t.datetime "date"
    t.time "opening_hour"
    t.time "closing_hour"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["website_id", "date"], name: "index_opening_hours_on_website_id_and_date", unique: true
    t.index ["website_id"], name: "index_opening_hours_on_website_id"
  end

  create_table "page_blocks", force: :cascade do |t|
    t.bigint "block_id", null: false
    t.bigint "page_id", null: false
    t.integer "position", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["block_id", "page_id", "position"], name: "index_page_blocks_on_block_id_and_page_id_and_position", unique: true
    t.index ["block_id"], name: "index_page_blocks_on_block_id"
    t.index ["page_id"], name: "index_page_blocks_on_page_id"
  end

  create_table "pages", force: :cascade do |t|
    t.string "title"
    t.string "slug"
    t.datetime "published_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "ancestry", collation: "C"
    t.integer "ancestry_depth", default: 0
    t.string "meta_title"
    t.string "meta_description", limit: 500
    t.string "locale", default: "cs"
    t.integer "position"
    t.string "type"
    t.bigint "website_id"
    t.string "link"
    t.boolean "is_homepage", default: false
    t.bigint "anchor_block_id"
    t.bigint "translation_group_id"
    t.bigint "layout_id"
    t.index ["ancestry"], name: "index_pages_on_ancestry"
    t.index ["anchor_block_id"], name: "index_pages_on_anchor_block_id"
    t.index ["layout_id"], name: "index_pages_on_layout_id"
    t.index ["translation_group_id"], name: "index_pages_on_translation_group_id"
    t.index ["website_id"], name: "index_pages_on_website_id"
  end

  create_table "pricing", force: :cascade do |t|
    t.string "pricing_type"
    t.datetime "valid_from"
    t.datetime "valid_to"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "website_id", null: false
    t.integer "position"
    t.string "locale"
    t.string "name"
    t.index ["website_id"], name: "index_pricing_on_website_id"
  end

  create_table "pricing_items", force: :cascade do |t|
    t.bigint "pricing_section_id", null: false
    t.decimal "price", precision: 10, scale: 2
    t.decimal "price_eur", precision: 10, scale: 2
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.hstore "name", default: {}, null: false
    t.hstore "description", default: {}, null: false
    t.integer "duration"
    t.index ["pricing_section_id"], name: "index_pricing_items_on_pricing_section_id"
  end

  create_table "pricing_sections", force: :cascade do |t|
    t.bigint "pricing_id", null: false
    t.string "name"
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.date "valid_date"
    t.index ["pricing_id"], name: "index_pricing_sections_on_pricing_id"
  end

  create_table "reservations", force: :cascade do |t|
    t.bigint "website_id", null: false
    t.string "name"
    t.string "email"
    t.string "phone"
    t.date "date"
    t.datetime "time"
    t.integer "guests"
    t.text "note"
    t.integer "status", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["website_id"], name: "index_reservations_on_website_id"
  end

  create_table "reviews", force: :cascade do |t|
    t.string "name"
    t.text "content"
    t.text "origin_id"
    t.string "origin_name"
    t.integer "rating"
    t.datetime "published_at"
    t.text "origin_url"
    t.datetime "approved_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "website_id", null: false
    t.datetime "removed_at"
    t.index ["website_id"], name: "index_reviews_on_website_id"
  end

  create_table "services", force: :cascade do |t|
    t.bigint "website_id", null: false
    t.string "type"
    t.jsonb "options"
    t.datetime "processed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["website_id"], name: "index_services_on_website_id"
  end

  create_table "sessions", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "ip_address"
    t.string "user_agent"
    t.index ["user_id"], name: "index_sessions_on_user_id"
  end

  create_table "solid_queue_blocked_executions", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.string "queue_name", null: false
    t.integer "priority", default: 0, null: false
    t.string "concurrency_key", null: false
    t.datetime "expires_at", null: false
    t.datetime "created_at", null: false
    t.index ["concurrency_key", "priority", "job_id"], name: "index_solid_queue_blocked_executions_for_release"
    t.index ["expires_at", "concurrency_key"], name: "index_solid_queue_blocked_executions_for_maintenance"
    t.index ["job_id"], name: "index_solid_queue_blocked_executions_on_job_id", unique: true
  end

  create_table "solid_queue_claimed_executions", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.bigint "process_id"
    t.datetime "created_at", null: false
    t.index ["job_id"], name: "index_solid_queue_claimed_executions_on_job_id", unique: true
    t.index ["process_id", "job_id"], name: "index_solid_queue_claimed_executions_on_process_id_and_job_id"
  end

  create_table "solid_queue_failed_executions", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.text "error"
    t.datetime "created_at", null: false
    t.index ["job_id"], name: "index_solid_queue_failed_executions_on_job_id", unique: true
  end

  create_table "solid_queue_jobs", force: :cascade do |t|
    t.string "queue_name", null: false
    t.string "class_name", null: false
    t.text "arguments"
    t.integer "priority", default: 0, null: false
    t.string "active_job_id"
    t.datetime "scheduled_at"
    t.datetime "finished_at"
    t.string "concurrency_key"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active_job_id"], name: "index_solid_queue_jobs_on_active_job_id"
    t.index ["class_name"], name: "index_solid_queue_jobs_on_class_name"
    t.index ["finished_at"], name: "index_solid_queue_jobs_on_finished_at"
    t.index ["queue_name", "finished_at"], name: "index_solid_queue_jobs_for_filtering"
    t.index ["scheduled_at", "finished_at"], name: "index_solid_queue_jobs_for_alerting"
  end

  create_table "solid_queue_pauses", force: :cascade do |t|
    t.string "queue_name", null: false
    t.datetime "created_at", null: false
    t.index ["queue_name"], name: "index_solid_queue_pauses_on_queue_name", unique: true
  end

  create_table "solid_queue_processes", force: :cascade do |t|
    t.string "kind", null: false
    t.datetime "last_heartbeat_at", null: false
    t.bigint "supervisor_id"
    t.integer "pid", null: false
    t.string "hostname"
    t.text "metadata"
    t.datetime "created_at", null: false
    t.string "name", null: false
    t.index ["last_heartbeat_at"], name: "index_solid_queue_processes_on_last_heartbeat_at"
    t.index ["name", "supervisor_id"], name: "index_solid_queue_processes_on_name_and_supervisor_id", unique: true
    t.index ["supervisor_id"], name: "index_solid_queue_processes_on_supervisor_id"
  end

  create_table "solid_queue_ready_executions", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.string "queue_name", null: false
    t.integer "priority", default: 0, null: false
    t.datetime "created_at", null: false
    t.index ["job_id"], name: "index_solid_queue_ready_executions_on_job_id", unique: true
    t.index ["priority", "job_id"], name: "index_solid_queue_poll_all"
    t.index ["queue_name", "priority", "job_id"], name: "index_solid_queue_poll_by_queue"
  end

  create_table "solid_queue_recurring_executions", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.string "task_key", null: false
    t.datetime "run_at", null: false
    t.datetime "created_at", null: false
    t.index ["job_id"], name: "index_solid_queue_recurring_executions_on_job_id", unique: true
    t.index ["task_key", "run_at"], name: "index_solid_queue_recurring_executions_on_task_key_and_run_at", unique: true
  end

  create_table "solid_queue_recurring_tasks", force: :cascade do |t|
    t.string "key", null: false
    t.string "schedule", null: false
    t.string "command", limit: 2048
    t.string "class_name"
    t.text "arguments"
    t.string "queue_name"
    t.integer "priority", default: 0
    t.boolean "static", default: true, null: false
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_solid_queue_recurring_tasks_on_key", unique: true
    t.index ["static"], name: "index_solid_queue_recurring_tasks_on_static"
  end

  create_table "solid_queue_scheduled_executions", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.string "queue_name", null: false
    t.integer "priority", default: 0, null: false
    t.datetime "scheduled_at", null: false
    t.datetime "created_at", null: false
    t.index ["job_id"], name: "index_solid_queue_scheduled_executions_on_job_id", unique: true
    t.index ["scheduled_at", "priority", "job_id"], name: "index_solid_queue_dispatch_all"
  end

  create_table "solid_queue_semaphores", force: :cascade do |t|
    t.string "key", null: false
    t.integer "value", default: 1, null: false
    t.datetime "expires_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["expires_at"], name: "index_solid_queue_semaphores_on_expires_at"
    t.index ["key", "value"], name: "index_solid_queue_semaphores_on_key_and_value"
    t.index ["key"], name: "index_solid_queue_semaphores_on_key", unique: true
  end

  create_table "templates", force: :cascade do |t|
    t.bigint "website_id", null: false
    t.string "name"
    t.string "color_theme"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["website_id"], name: "index_templates_on_website_id"
  end

  create_table "themes", force: :cascade do |t|
    t.jsonb "options"
    t.bigint "website_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["website_id"], name: "index_themes_on_website_id"
  end

  create_table "translation_jobs", force: :cascade do |t|
    t.string "translatable_type", null: false
    t.bigint "translatable_id", null: false
    t.string "source_locale", null: false
    t.string "target_locale", null: false
    t.jsonb "source_content", default: {}, null: false
    t.jsonb "translated_content", default: {}, null: false
    t.integer "status", default: 0, null: false
    t.text "error_message"
    t.datetime "processed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["processed_at"], name: "index_translation_jobs_on_processed_at"
    t.index ["status"], name: "index_translation_jobs_on_status"
    t.index ["translatable_type", "translatable_id", "target_locale"], name: "index_translation_jobs_on_translatable_and_target_locale"
    t.index ["translatable_type", "translatable_id"], name: "index_translation_jobs_on_translatable"
  end

  create_table "users", force: :cascade do |t|
    t.string "name"
    t.string "email"
    t.string "password_digest"
    t.string "first_name"
    t.string "last_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "remember_token"
  end

  create_table "webhooks", force: :cascade do |t|
    t.string "event_type"
    t.jsonb "payload"
    t.datetime "processed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "websites", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.string "name"
    t.string "address"
    t.string "city"
    t.string "postal_code"
    t.string "country"
    t.string "phone"
    t.string "email"
    t.string "domain"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "locale", default: "cs"
    t.jsonb "available_locales", default: []
    t.jsonb "data", default: {}
    t.string "map_url"
    t.jsonb "social_networks", default: {}
    t.jsonb "theme", default: {}
    t.index ["account_id"], name: "index_websites_on_account_id"
  end

  add_foreign_key "account_users", "accounts"
  add_foreign_key "account_users", "users"
  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "block_controls", "blocks"
  add_foreign_key "blocks", "media_collections"
  add_foreign_key "blocks", "media_types"
  add_foreign_key "blocks", "pricing"
  add_foreign_key "forms", "websites"
  add_foreign_key "html_tags", "pages"
  add_foreign_key "images", "websites"
  add_foreign_key "inbox_messages", "forms"
  add_foreign_key "inbox_messages", "websites"
  add_foreign_key "layout_blocks", "blocks"
  add_foreign_key "layout_blocks", "layouts"
  add_foreign_key "layouts", "blocks", column: "main_footer_block_id"
  add_foreign_key "layouts", "blocks", column: "main_header_block_id"
  add_foreign_key "layouts", "websites"
  add_foreign_key "media", "icons"
  add_foreign_key "media", "media_collections"
  add_foreign_key "media", "media_types"
  add_foreign_key "media", "websites"
  add_foreign_key "media_collections", "media_types"
  add_foreign_key "media_collections", "websites"
  add_foreign_key "media_contents", "media", column: "media_id"
  add_foreign_key "media_fields", "media_types"
  add_foreign_key "opening_hours", "websites"
  add_foreign_key "page_blocks", "blocks"
  add_foreign_key "page_blocks", "pages"
  add_foreign_key "pages", "blocks", column: "anchor_block_id"
  add_foreign_key "pages", "layouts"
  add_foreign_key "pages", "pages", column: "translation_group_id"
  add_foreign_key "pages", "websites"
  add_foreign_key "pricing", "websites"
  add_foreign_key "pricing_items", "pricing_sections"
  add_foreign_key "pricing_sections", "pricing"
  add_foreign_key "reservations", "websites"
  add_foreign_key "reviews", "websites"
  add_foreign_key "services", "websites"
  add_foreign_key "sessions", "users"
  add_foreign_key "solid_queue_blocked_executions", "solid_queue_jobs", column: "job_id", on_delete: :cascade
  add_foreign_key "solid_queue_claimed_executions", "solid_queue_jobs", column: "job_id", on_delete: :cascade
  add_foreign_key "solid_queue_failed_executions", "solid_queue_jobs", column: "job_id", on_delete: :cascade
  add_foreign_key "solid_queue_ready_executions", "solid_queue_jobs", column: "job_id", on_delete: :cascade
  add_foreign_key "solid_queue_recurring_executions", "solid_queue_jobs", column: "job_id", on_delete: :cascade
  add_foreign_key "solid_queue_scheduled_executions", "solid_queue_jobs", column: "job_id", on_delete: :cascade
  add_foreign_key "templates", "websites"
  add_foreign_key "themes", "websites"
  add_foreign_key "websites", "accounts"
end
