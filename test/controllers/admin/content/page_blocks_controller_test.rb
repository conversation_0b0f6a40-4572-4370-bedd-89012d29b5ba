require 'test_helper'

class Admin::Content::PageBlocksControllerTest < ActionDispatch::IntegrationTest
  setup do
    @website = websites(:one)
    @page = pages(:one)
    @block = blocks(:one)
    
    # Simulace přihlášení
    Current.website = @website
  end

  test "should show references when add=references" do
    # Vytvo<PERSON>íme <PERSON>, k<PERSON><PERSON> bude k dispozici jako reference
    reference_block = Block.create!(name: "test_block")
    reference_block.page_blocks.create!(page: pages(:two), position: 1)
    
    get new_admin_content_page_block_path(@page, add: 'references')
    
    assert_response :success
    assert_select 'h4', text: 'Dříve vytvořené bloky'
  end

  test "should create reference to existing block" do
    # Vytvoříme blok na jiné stránce
    other_page = pages(:two)
    reference_block = Block.create!(name: "reference_test")
    reference_block.page_blocks.create!(page: other_page, position: 1)
    
    assert_difference '@page.blocks.count', 1 do
      post create_reference_admin_content_page_blocks_path(@page), 
           params: { block_id: reference_block.id }
    end
    
    assert_redirected_to admin_content_page_blocks_path(@page)
    assert @page.blocks.include?(reference_block)
  end

  test "should create block controls for page locale when creating reference" do
    # Vytvoříme blok s controls v jiné lokalizaci
    reference_block = Block.create!(name: "reference_test")
    reference_block.page_blocks.create!(page: pages(:two), position: 1)
    
    # Přidáme control v češtině
    reference_block.controls.create!(
      type: "BlockControls::Heading",
      locale: "cs",
      text: "Czech heading",
      position: 1
    )
    
    # Nastavíme stránku na anglickou lokalizaci
    @page.update!(locale: "en")
    
    post create_reference_admin_content_page_blocks_path(@page), 
         params: { block_id: reference_block.id }
    
    # Ověříme, že byl vytvořen control pro anglickou lokalizaci
    english_controls = reference_block.controls.for_locale("en")
    assert english_controls.any?, "Should create controls for page locale"
    assert_equal "Czech heading", english_controls.first.text
  end
end
