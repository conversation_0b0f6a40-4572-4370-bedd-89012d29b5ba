require 'test_helper'

class BlockBuilderTest < ActiveSupport::TestCase
  test "builds a block object from yaml" do
    block_object = BlockBuilder.build("hero001")
    
    assert_equal "hero001", block_object.name
    assert_equal :frontend, block_object.context
  end
  
  test "builds controls for a block" do
    controls = [
      { type: "BlockControls::Heading", text: "Test Heading", options: { heading_type: "h1" } },
      { type: "BlockControls::Paragraph", text: "Test Paragraph" }
    ]
    
    built_controls = BlockBuilder.build_controls(controls, :frontend)
    
    assert_equal 2, built_controls.size
    assert_instance_of HeadingPresenter, built_controls.first
    assert_instance_of ParagraphPresenter, built_controls.last
  end
  
  test "builds media for a block" do
    media_config = {
      type: "gallery",
      layout: "grid",
      gap: 10,
      media_items: [
        { title: "Item 1", type: "image", image_url: "image1.jpg" }
      ]
    }
    
    media = BlockBuilder.build_media(media_config)
    
    assert_instance_of MediaPresenter, media
    assert_equal "gallery", media.type
    assert_equal 1, media.items.size
  end
end
