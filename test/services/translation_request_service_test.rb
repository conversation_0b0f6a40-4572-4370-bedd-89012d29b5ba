require 'test_helper'

class TranslationRequestServiceTest < ActiveSupport::TestCase
  def setup
    @website = websites(:one)
    @media = media(:one)
    @media_content = MediaContent.create!(
      media: @media,
      locale: 'cs',
      title: 'Testovac<PERSON> titulek',
      text: 'Testovací text obsahu',
      caption: 'Testovac<PERSON> popisek'
    )
  end

  test "creates translation job for MediaContent" do
    service = TranslationRequestService.new(@media_content, 'en')
    
    assert_difference 'TranslationJob.count', 1 do
      job = service.call
      
      assert_equal @media_content, job.translatable
      assert_equal 'cs', job.source_locale
      assert_equal 'en', job.target_locale
      assert_equal 'pending', job.status
      assert job.source_content.present?
      assert_equal 'Testovací titulek', job.source_content['title']
      assert_equal 'Testovací text obsahu', job.source_content['text']
      assert_equal 'Testovací popisek', job.source_content['caption']
    end
  end

  test "prevents duplicate jobs" do
    service = TranslationRequestService.new(@media_content, 'en')
    
    # Vytvoříme první úlohu
    first_job = service.call
    
    # Pokus o vytvoření d<PERSON> by měl selhat
    assert_raises TranslationRequestService::DuplicateJobError do
      service.call
    end
  end

  test "allows new job after previous job is completed" do
    service = TranslationRequestService.new(@media_content, 'en')
    
    # Vytvoříme a dokončíme první úlohu
    first_job = service.call
    first_job.mark_as_completed!({})
    
    # Nyní by mělo být možné vytvořit novou úlohu
    assert_difference 'TranslationJob.count', 1 do
      second_job = service.call
      assert_not_equal first_job.id, second_job.id
    end
  end

  test "validates supported object types" do
    unsupported_object = @website
    
    assert_raises TranslationRequestService::UnsupportedObjectError do
      TranslationRequestService.new(unsupported_object, 'en').call
    end
  end

  test "validates different source and target locales" do
    assert_raises ArgumentError do
      TranslationRequestService.new(@media_content, 'cs').call
    end
  end

  test "validates supported target locale" do
    assert_raises ArgumentError do
      TranslationRequestService.new(@media_content, 'unsupported').call
    end
  end
end
